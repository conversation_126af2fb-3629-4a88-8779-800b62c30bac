# Windows OpenSSH Setup Script
# This script installs and configures OpenSSH Client for the SSH Key Manager

Write-Host "=== SSH Key Manager - Windows OpenSSH Setup ===" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  This script requires Administrator privileges to install OpenSSH." -ForegroundColor Yellow
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To run as Administrator:" -ForegroundColor White
    Write-Host "1. Right-click on PowerShell" -ForegroundColor White
    Write-Host "2. Select 'Run as Administrator'" -ForegroundColor White
    Write-Host "3. Navigate to this directory and run: .\setup_windows_openssh.ps1" -ForegroundColor White
    exit 1
}

Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green

# Check if OpenSSH Client is already installed
Write-Host "Checking OpenSSH Client installation..." -ForegroundColor White

$opensshClient = Get-WindowsCapability -Online | Where-Object Name -like 'OpenSSH.Client*'

if ($opensshClient.State -eq "Installed") {
    Write-Host "✓ OpenSSH Client is already installed" -ForegroundColor Green
} else {
    Write-Host "Installing OpenSSH Client..." -ForegroundColor Yellow
    try {
        Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******
        Write-Host "✓ OpenSSH Client installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to install OpenSSH Client: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Check and configure SSH Agent service
Write-Host "Configuring SSH Agent service..." -ForegroundColor White

try {
    $sshAgentService = Get-Service ssh-agent -ErrorAction SilentlyContinue
    
    if ($null -eq $sshAgentService) {
        Write-Host "✗ SSH Agent service not found" -ForegroundColor Red
        exit 1
    }
    
    if ($sshAgentService.StartType -eq "Disabled") {
        Write-Host "Enabling SSH Agent service..." -ForegroundColor Yellow
        Set-Service -Name ssh-agent -StartupType Manual
        Write-Host "✓ SSH Agent service enabled (Manual startup)" -ForegroundColor Green
    } else {
        Write-Host "✓ SSH Agent service is already enabled" -ForegroundColor Green
    }
    
    # Start the service if it's not running
    if ($sshAgentService.Status -ne "Running") {
        Write-Host "Starting SSH Agent service..." -ForegroundColor Yellow
        Start-Service ssh-agent
        Write-Host "✓ SSH Agent service started" -ForegroundColor Green
    } else {
        Write-Host "✓ SSH Agent service is already running" -ForegroundColor Green
    }
    
} catch {
    Write-Host "✗ Failed to configure SSH Agent service: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
Write-Host ""
Write-Host "Verifying installation..." -ForegroundColor White

$sshKeygen = Get-Command ssh-keygen -ErrorAction SilentlyContinue
$sshAgent = Get-Command ssh-agent -ErrorAction SilentlyContinue
$sshAdd = Get-Command ssh-add -ErrorAction SilentlyContinue

if ($sshKeygen -and $sshAgent -and $sshAdd) {
    Write-Host "✓ All SSH tools are available:" -ForegroundColor Green
    Write-Host "  - ssh-keygen: $($sshKeygen.Source)" -ForegroundColor Gray
    Write-Host "  - ssh-agent: $($sshAgent.Source)" -ForegroundColor Gray
    Write-Host "  - ssh-add: $($sshAdd.Source)" -ForegroundColor Gray
} else {
    Write-Host "✗ Some SSH tools are missing" -ForegroundColor Red
    if (-not $sshKeygen) { Write-Host "  - ssh-keygen: Not found" -ForegroundColor Red }
    if (-not $sshAgent) { Write-Host "  - ssh-agent: Not found" -ForegroundColor Red }
    if (-not $sshAdd) { Write-Host "  - ssh-add: Not found" -ForegroundColor Red }
    exit 1
}

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host "OpenSSH Client is now properly installed and configured." -ForegroundColor White
Write-Host "You can now run the SSH Key Manager without issues." -ForegroundColor White
Write-Host ""
Write-Host "To run the SSH Key Manager:" -ForegroundColor Cyan
Write-Host "  .\src\main.bat" -ForegroundColor White
Write-Host ""
