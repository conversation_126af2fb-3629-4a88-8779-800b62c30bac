@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: Define key names and default email
SET "ACCOUNT_NAME=jh.paulsen.wrk"
SET "ACCOUNT_EMAIL=%<EMAIL>"
SET "PRIVATE_KEY=%COMPUTERNAME%.%ACCOUNT_NAME%.id_rsa"
SET "SSH_PATH=%USERPROFILE%\.ssh"

:: CREATE KEY
:: ---
:: Generate SSH key
ssh-keygen -o -t rsa -b 4096 -C "%ACCOUNT_EMAIL%" -f "%SSH_PATH%\%PRIVATE_KEY%" -N ""

:: CLIENT
:: ---

:: # restart ssh agent
:: :: start-ssh-agent.cmd
:: ssh-agent -k
:: ssh-agent -s
:: ssh-agent

:: # list existing keys
:: :: ssh-add -l

:: # remove all keys
:: :: ssh-add -D

:: # add keyfile to agent and test connection
:: ssh-add ~/.ssh/DSK.jh.paulsen.wrk.id_rsa
:: ssh-add "C:\Users\<USER>\.ssh\DSK.jh.paulsen.wrk.id_rsa"
:: ssh -T **************

:: # clone
:: git clone "https://gitlab.com/j.workflow/git_work_jorn.git" Git_Work_Jorn
:: :: git clone "**************:j.workflow/git_work_jorn.git" Git_Work_Jorn
:: :: git clone "https://gitlab.com/j.workflow/git_work_jorn.git" Git_Work_Jorn

:: # add and verify remote
:: git init
:: git remote add origin "**************:j.workflow/git_work_jorn.git"
:: git remote -v

:: # add and verify remote

:: Display the public key
:: - E.g: ssh-add ~/.ssh/DSK.jh.paulsen.wrk.id_rsa.pub
:: - E.g: ssh-add DSK.jh.paulsen.wrk.id_rsa.pub
:: - E.g: ssh-add C:\Users\<USER>\.ssh\DSK.jh.paulsen.wrk.id_rsa.pub
ECHO.
ECHO.
ECHO Your public key is:
ECHO.
TYPE "%SSH_PATH%\%PRIVATE_KEY%.pub"

:: Finished
ECHO.
ECHO.
ECHO Please add the above public key to your GitLab SSH keys at:
ECHO https://gitlab.com/-/profile/keys
ECHO.
PAUSE > NUL

:: usage: ssh-keygen [-q] [-b bits] [-C comment] [-f output_keyfile] [-m format]
::                   [-N new_passphrase] [-t dsa | ecdsa | ed25519 | rsa]
::        ssh-keygen -p [-f keyfile] [-m format] [-N new_passphrase]
::                    [-P old_passphrase]
::        ssh-keygen -i [-f input_keyfile] [-m key_format]
::        ssh-keygen -e [-f input_keyfile] [-m key_format]
::        ssh-keygen -y [-f input_keyfile]
::        ssh-keygen -c [-C comment] [-f keyfile] [-P passphrase]
::        ssh-keygen -l [-v] [-E fingerprint_hash] [-f input_keyfile]
::        ssh-keygen -B [-f input_keyfile]
::        ssh-keygen -D pkcs11
::        ssh-keygen -F hostname [-lv] [-f known_hosts_file]
::        ssh-keygen -H [-f known_hosts_file]
::        ssh-keygen -R hostname [-f known_hosts_file]
::        ssh-keygen -r hostname [-g] [-f input_keyfile]
::        ssh-keygen -G output_file [-v] [-b bits] [-M memory] [-S start_point]
::        ssh-keygen -f input_file -T output_file [-v] [-a rounds] [-J num_lines]
::                   [-j start_line] [-K checkpt] [-W generator]
::        ssh-keygen -I certificate_identity -s ca_key [-hU] [-D pkcs11_provider]
::                   [-n principals] [-O option] [-V validity_interval]
::                   [-z serial_number] file ...
::        ssh-keygen -L [-f input_keyfile]
::        ssh-keygen -A [-f prefix_path]
::        ssh-keygen -k -f krl_file [-u] [-s ca_public] [-z version_number]
::                   file ...
::        ssh-keygen -Q -f krl_file file ...
::        ssh-keygen -Y check-novalidate -n namespace -s signature_file
::        ssh-keygen -Y sign -f key_file -n namespace file ...
::        ssh-keygen -Y verify -f allowed_signers_file -I signer_identity
::                 -n namespace -s signature_file [-r revocation_file]