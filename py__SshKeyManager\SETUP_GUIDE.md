# SSH Key Manager - Setup Guide

This guide will help you set up the SSH Key Manager on a fresh Windows 11 installation, resolving common dependency issues.

## Quick Start (Recommended)

### Option 1: Automatic Setup with PowerShell

1. **Open PowerShell as Administrator**
   - Right-click on PowerShell
   - Select "Run as Administrator"

2. **Run the setup script**
   ```powershell
   cd path\to\py__SshKeyManager
   .\setup_windows_openssh.ps1
   ```

3. **Initialize Python environment**
   ```powershell
   .\py_venv_init.bat
   ```

4. **Run the SSH Key Manager**
   ```powershell
   .\src\main.bat
   ```

### Option 2: Using uv (Modern Python Package Manager)

1. **Install uv** (if not already installed)
   ```powershell
   # Using pip
   pip install uv
   
   # Or using pipx (recommended)
   pipx install uv
   ```

2. **Setup with uv**
   ```powershell
   cd path\to\py__SshKeyManager
   
   # Create virtual environment and install dependencies
   uv venv
   uv pip install -e .
   
   # Install development dependencies (optional)
   uv pip install -e ".[dev]"
   ```

3. **Setup OpenSSH** (run as Administrator)
   ```powershell
   .\setup_windows_openssh.ps1
   ```

4. **Run the application**
   ```powershell
   # Activate environment
   .venv\Scripts\activate
   
   # Run the SSH Key Manager
   python src\main.py
   ```

## Manual Setup (If Automatic Setup Fails)

### Step 1: Install OpenSSH Client

1. **Open PowerShell as Administrator**

2. **Install OpenSSH Client**
   ```powershell
   Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******
   ```

3. **Enable SSH Agent Service**
   ```powershell
   Set-Service -Name ssh-agent -StartupType Manual
   Start-Service ssh-agent
   ```

4. **Verify Installation**
   ```powershell
   ssh-keygen --help
   ssh-agent
   ssh-add -l
   ```

### Step 2: Setup Python Environment

1. **Ensure Python 3.8+ is installed**
   ```powershell
   python --version
   ```

2. **Create virtual environment**
   ```powershell
   python -m venv venv
   venv\Scripts\activate
   ```

3. **No additional packages needed** - The SSH Key Manager uses only Python standard library!

## Troubleshooting Common Issues

### Error 1058: "The service cannot be started"

**Cause**: SSH Agent service is disabled or OpenSSH Client is not installed.

**Solution**:
```powershell
# Run as Administrator
Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******
Set-Service -Name ssh-agent -StartupType Manual
Start-Service ssh-agent
```

### "ssh-keygen not found"

**Cause**: OpenSSH Client is not installed or not in PATH.

**Solution**:
1. Install OpenSSH Client (see Step 1 above)
2. Restart your terminal
3. Verify with: `where ssh-keygen`

### Permission Denied Errors

**Cause**: Insufficient privileges for service management.

**Solution**:
- Always run setup scripts as Administrator
- Regular usage doesn't require Administrator privileges

## Why Use uv?

uv is a modern, fast Python package manager that offers several advantages:

### Benefits of uv:
- **Speed**: 10-100x faster than pip
- **Reliability**: Better dependency resolution
- **Consistency**: Lock files for reproducible environments
- **Modern**: Built with Rust, actively maintained
- **Compatibility**: Drop-in replacement for pip

### uv Commands for This Project:
```powershell
# Create environment
uv venv

# Install project
uv pip install -e .

# Add development dependencies
uv add --dev pytest black flake8

# Update dependencies
uv pip sync requirements.txt

# Run with specific Python version
uv python install 3.11
uv venv --python 3.11
```

## Project Structure

```
py__SshKeyManager/
├── src/
│   ├── main.py              # Main application
│   └── main.bat             # Windows launcher
├── setup_windows_openssh.ps1 # OpenSSH setup script
├── py_venv_init.bat         # Python environment setup
├── pyproject.toml           # Modern Python project config
├── requirements.txt         # Dependency documentation
└── SETUP_GUIDE.md          # This guide
```

## Features

- **Zero Python Dependencies**: Uses only standard library
- **Automatic OpenSSH Setup**: Detects and fixes missing dependencies
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Git Integration**: Test connections, clone repos, manage remotes
- **Key Management**: Generate, view, copy SSH keys
- **Agent Management**: Start/stop SSH agent, add/remove keys

## Next Steps

After setup, you can:
1. Create your first SSH key
2. Add it to your Git service (GitHub, GitLab, etc.)
3. Test the connection
4. Clone repositories using SSH

For detailed usage instructions, run the application and explore the interactive menu!
