# SSH Key Manager - No Runtime Dependencies
#
# The core application uses only Python standard library modules.
# No runtime dependencies are required to run the application.
#
# Build dependencies (only when using `uv pip install -e .`):
# - hatchling (build backend specified in pyproject.toml)
#
# Development dependencies (optional):
# - pytest, black, flake8 (install with `uv pip install -e ".[dev]"`)
