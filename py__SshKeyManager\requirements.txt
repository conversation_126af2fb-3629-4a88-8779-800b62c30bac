# SSH Key Manager Dependencies
#
# This project uses minimal dependencies to ensure compatibility
# and easy setup on fresh Windows installations.
#
# Core dependencies (all built-in to Python 3.6+):
# - argparse (built-in)
# - pathlib (built-in)
# - platform (built-in)
# - shutil (built-in)
# - subprocess (built-in)
# - sys (built-in)
#
# No external dependencies required!
# The SSH Key Manager uses only Python standard library modules.
#
# For development/testing (optional):
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
#
# To install development dependencies:
# pip install pytest black flake8
#
# Or with uv (recommended):
# uv add --dev pytest black flake8
