:: =============================================================================
:: Understanding the Environment
:: =============================================================================
::
:: This project uses a structured batch script approach for Python environment management:
::
:: 1. `py_venv_init.bat` - Creates and initializes a Python virtual environment
:: 2. `main.bat` - Runs the main Python application after activating the virtual environment
:: 3. `requirements.txt` - Should contain dependencies (appears to be empty currently)
::
:: Environment Setup Plan
:: 1. **Initialize the Virtual Environment**:
::    - We'll execute `py_venv_init.bat` from the current directory
::    - This script will:
::      - Find available Python installations
::      - Prompt to select a version if multiple are found
::      - Create a virtual environment named "venv"
::      - Install/upgrade pip
::      - Install any packages listed in requirements.txt
::      - Ask if you want to upgrade packages
:: 2. **Running the Application**:
::    - After environment setup, we'll use `main.bat` to run the application
::    - This will:
::      - Locate and activate the virtual environment
::      - Execute `main.py` with the `--prompt` flag
::
:: Command Execution
::    - For Windows 11, we'll use Command Prompt or PowerShell to execute the batch files. The correct commands would be:
::
:: ```
:: # For initializing the environment:
:: .\py_venv_init.bat
:: # After initialization, to run the application:
:: .\main.bat
:: ```


:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=.venv"
TITLE (py) %~nx0


:: =============================================================================
:: py: version
:: =============================================================================
SET /A "py_count=0"
FOR /F "delims=" %%p IN ('WHERE python 2^>nul') DO (
    FOR /F "tokens=*" %%v IN ('"%%p" --version 2^>^&1 2^>nul') DO (
        ECHO %%v | FIND "Python " > NUL && (
            SET /A "py_count+=1"
            SET "py_version!py_count!=%%v"
            SET "python_!py_count!=%%p"
        )
    )
)
IF %py_count% EQU 0 (
    ECHO No valid Python installations found. & ECHO. & PAUSE>NUL & EXIT /B
) ELSE IF %py_count% EQU 1 (SET "py_path=%python_1%") ELSE (
    ECHO Choose Version: & FOR /L %%i IN (1,1,%py_count%) DO (
        ECHO - [%%i] !py_version%%i! - !python_%%i!
    )
    ECHO. & SET /P version="Choose the Python version to use (enter number): "
    CALL SET "py_path=%%python_!version!%%" & ECHO.
)


:: =============================================================================
:: venv: create/initialize/activate
:: =============================================================================
SET "__venv_stem__=%__init_path__%"
IF NOT EXIST "%__venv_stem__%\Scripts\python.exe" ("!py_path!" -m venv "%__venv_name__%")
CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"


:: :: =============================================================================
:: :: .gitignore: write
:: :: =============================================================================
:: SETLOCAL DISABLEDELAYEDEXPANSION
:: ECHO * > "%__venv_stem__%\%__venv_name__%\.gitignore"
:: ECHO !.gitignore >> "%__venv_stem__%\%__venv_name__%\.gitignore"
:: ECHO * > "%__venv_stem__%\%__venv_name__%\.stignore"
:: ECHO !.stignore >> "%__venv_stem__%\%__venv_name__%\.stignore"
:: ENDLOCAL


:: =============================================================================
:: requirements.txt: install
:: =============================================================================
SET "requirements_txt=%__venv_stem__%\requirements.txt"
IF EXIST "%requirements_txt%" (
    "python" -m pip install --upgrade pip
    "python" -m pip install -r "%requirements_txt%"
    IF ERRORLEVEL 1 (
        ECHO Failed to install packages from "%requirements_txt%"
    )
)


:: =============================================================================
:: requirements.txt: upgrade
:: =============================================================================
IF EXIST "%requirements_txt%" (
    ECHO. & ECHO Do you want to upgrade the packages? ^(press 'y' to upgrade...^)
    SET /P upgrade_packages=">> "
    IF "!upgrade_packages!"=="y" (
        "python" -m pip install --upgrade -r "%requirements_txt%"
        IF ERRORLEVEL 1 (
            ECHO Failed to upgrade packages from "%requirements_txt%"
        )
    )
)


:: =============================================================================
:: requirements.txt: preserved (contains project documentation)
:: =============================================================================
:: Note: requirements.txt is maintained manually and contains project info
:: It is not overwritten by pip freeze to preserve documentation


:: =============================================================================
:: cmd: exit
:: =============================================================================
ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
EXIT /B
