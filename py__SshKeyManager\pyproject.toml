[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ssh-key-manager"
version = "1.0.0"
description = "A comprehensive SSH key management tool for Windows with automatic OpenSSH setup"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "SSH Key Manager", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Version Control :: Git",
    "Topic :: System :: Systems Administration",
    "Topic :: Security :: Cryptography",
]
requires-python = ">=3.8"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
ssh-key-manager = "src.main:main"

[project.urls]
Homepage = "https://github.com/your-username/ssh-key-manager"
Repository = "https://github.com/your-username/ssh-key-manager.git"
Issues = "https://github.com/your-username/ssh-key-manager/issues"

[tool.black]
line-length = 79
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 79
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "build",
    "dist",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

# uv-specific configuration
[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=22.0.0", 
    "flake8>=4.0.0",
    "mypy>=1.0.0",
]
