#!/usr/bin/env python3
"""SSH Key Manager - Minimalist SSH key generation and management utility."""

import argparse
import platform
import shutil
import subprocess
import sys
from pathlib import Path

DEFAULT_ACCOUNT_NAME = "jh.paulsen.wrk"
DEFAULT_EMAIL_DOMAIN = "gmail.com"


def get_hostname():
    return platform.node().split(".")[0]


def get_ssh_dir():
    return Path.home() / ".ssh"


def get_key_path(name=None):
    ssh_dir = get_ssh_dir()
    hostname = get_hostname()
    if not name:
        name = DEFAULT_ACCOUNT_NAME
    key_name = f"{hostname}.{name}.id_rsa"
    return ssh_dir / key_name


def run_command(cmd, input_data=None):
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, input=input_data
        )
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


def copy_to_clipboard(text):
    try:
        if platform.system() == 'Windows':
            cmd = ['clip']
        elif platform.system() == 'Darwin':
            cmd = ['pbcopy']
        else:
            cmd = ['xclip', '-selection', 'clipboard']
        run_command(cmd, text)
        return True
    except Exception:
        return False


def create_key(name=None, email=None, key_type="rsa", key_bits=4096):
    if not name:
        name = DEFAULT_ACCOUNT_NAME
    if not email:
        email = f"{name}@{DEFAULT_EMAIL_DOMAIN}"
    
    key_path = get_key_path(name)
    ssh_dir = get_ssh_dir()
    ssh_dir.mkdir(exist_ok=True)
    
    cmd = [
        "ssh-keygen", "-o", "-t", key_type, "-b", str(key_bits),
        "-C", email, "-f", str(key_path), "-N", ""
    ]
    
    print(f"Generating {key_bits}-bit {key_type} key for {email}...")
    print(f"Key will be saved to: {key_path}")
    
    result = run_command(cmd)
    
    if result["success"]:
        return {
            "success": True,
            "key_path": key_path,
            "public_key_path": str(key_path) + ".pub",
            "message": f"Successfully generated key: {key_path}"
        }
    else:
        return {
            "success": False,
            "error": result.get("stderr", "Unknown error during key generation")
        }


def check_windows_openssh():
    if platform.system() != "Windows":
        return {"success": True, "message": "Not Windows - no check needed"}
    
    if not shutil.which("ssh-agent"):
        return {
            "success": False,
            "error": "OpenSSH Client not found",
            "fix_command": ('Add-WindowsCapability -Online '
                            '-Name OpenSSH.Client~~~~0.0.1.0')
        }
    
    check_service_cmd = [
        "powershell", "-Command",
        ("Get-Service ssh-agent -ErrorAction SilentlyContinue | "
         "Select-Object Status, StartType")
    ]
    result = run_command(check_service_cmd)
    
    if not result["success"] or "ssh-agent" not in result["stdout"]:
        return {
            "success": False,
            "error": "SSH Agent service not found",
            "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
        }
    
    if "Disabled" in result["stdout"]:
        return {
            "success": False,
            "error": "SSH Agent service is disabled",
            "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
        }
    
    return {"success": True, "message": "OpenSSH properly configured"}


def fix_windows_openssh():
    print("Attempting to fix OpenSSH configuration...")
    
    install_cmd = [
        "powershell", "-Command",
        "Add-WindowsCapability -Online -Name OpenSSH.Client~~~~0.0.1.0"
    ]
    print("Installing OpenSSH Client...")
    result = run_command(install_cmd)
    
    if not result["success"]:
        return {
            "success": False,
            "error": f"Failed to install OpenSSH Client: {result.get('stderr', 'Unknown error')}"
        }
    
    enable_cmd = [
        "powershell", "-Command",
        "Set-Service -Name ssh-agent -StartupType Manual"
    ]
    print("Enabling SSH Agent service...")
    result = run_command(enable_cmd)
    
    if not result["success"]:
        return {
            "success": False,
            "error": f"Failed to enable SSH Agent service: {result.get('stderr', 'Unknown error')}"
        }
    
    return {
        "success": True,
        "message": "OpenSSH Client installed and SSH Agent service enabled"
    }


def agent_operation(operation, key_path=None):
    if operation == "start":
        if platform.system() == "Windows":
            check_result = check_windows_openssh()
            if not check_result["success"]:
                print(f"⚠️  OpenSSH issue detected: {check_result['error']}")
                print("Attempting to fix automatically...")
                
                fix_result = fix_windows_openssh()
                if not fix_result["success"]:
                    return {
                        "success": False,
                        "error": f"OpenSSH configuration failed: {fix_result['error']}",
                        "manual_fix": f"Run as Administrator: {check_result.get('fix_command', 'Install OpenSSH')}"
                    }
                print(f"✓ {fix_result['message']}")
            
            print("Restarting SSH agent on Windows...")
            stop_cmd = ["powershell", "-Command", "Stop-Service ssh-agent -ErrorAction SilentlyContinue"]
            run_command(stop_cmd)
            
            start_cmd = ["powershell", "-Command", "Start-Service ssh-agent"]
            result = run_command(start_cmd)
            
            if not result["success"]:
                print("Using ssh-agent directly...")
                result = run_command(["ssh-agent", "-s"])
        else:
            print("Restarting SSH agent...")
            run_command(["ssh-agent", "-k"])
            result = run_command(["ssh-agent", "-s"])
        
        if result["success"]:
            return {"success": True, "message": "SSH agent started successfully"}
        else:
            return {"success": False, "error": result.get("stderr", "Failed to start SSH agent")}
            
    elif operation == "list":
        print("Listing keys in SSH agent...")
        result = run_command(["ssh-add", "-l"])
        
        if result["returncode"] == 0:
            if not result["stdout"].strip():
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": True, "keys": result["stdout"]}
        else:
            if "The agent has no identities" in result["stderr"]:
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": False, "error": result["stderr"]}
            
    elif operation == "add":
        if not key_path:
            key_path = get_key_path()
        
        print(f"Adding key to SSH agent: {key_path}")
        result = run_command(["ssh-add", str(key_path)])
        
        if result["success"]:
            return {"success": True, "message": f"Added key: {key_path}"}
        else:
            return {"success": False, "error": result.get("stderr", f"Failed to add key: {key_path}")}
            
    elif operation == "clear":
        print("Clearing all keys from SSH agent...")
        result = run_command(["ssh-add", "-D"])
        
        if result["success"]:
            return {"success": True, "message": "Cleared all keys from agent"}
        else:
            return {"success": False, "error": result.get("stderr", "Failed to clear keys")}
    
    return {"success": False, "error": f"Unknown operation: {operation}"}


if __name__ == "__main__":
    if not shutil.which("ssh-keygen"):
        print("ERROR: ssh-keygen not found. Please install SSH.")
        sys.exit(1)
    
    if len(sys.argv) == 1:
        print("===== SSH Key Manager =====")
        print("Run with --help for command line usage")
        print("Interactive mode: python main.py")
        sys.exit(0)
    
    # Command line mode would go here
    print("Command line mode not implemented in this simplified version")
