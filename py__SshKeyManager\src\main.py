#!/usr/bin/env python3

"""
SSH Key Manager - A minimalist utility for SSH key generation and management
Cross-platform replacement for git_wrk_generate_ssh.bat
"""

import argparse
import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path

# ====== CONFIGURATION ======

DEFAULT_ACCOUNT_NAME = "jh.paulsen.wrk"  # From original batch file
DEFAULT_EMAIL_DOMAIN = "gmail.com"       # From original batch file


# ====== UTILITY FUNCTIONS ======

def get_hostname():
    """Get the current hostname for key naming."""
    return platform.node().split(".")[0]


def get_ssh_dir():
    """Get the SSH directory path for the current platform."""
    return Path.home() / ".ssh"


def get_key_path(name=None):
    """Generate the full path to an SSH key."""
    ssh_dir = get_ssh_dir()
    hostname = get_hostname()
    
    if not name:
        name = DEFAULT_ACCOUNT_NAME
        
    key_name = f"{hostname}.{name}.id_rsa"
    return ssh_dir / key_name


def run_command(cmd, input_data=None):
    """Execute a command and return the result."""
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True,
            input=input_data
        )
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def copy_to_clipboard(text):
    """Copy text to clipboard based on platform."""
    try:
        if platform.system() == 'Windows':
            cmd = ['clip']
        elif platform.system() == 'Darwin':  # macOS
            cmd = ['pbcopy']
        else:  # Linux
            cmd = ['xclip', '-selection', 'clipboard']
            
        run_command(cmd, text)
        return True
    except Exception:
        return False


# ====== CORE FUNCTIONALITY ======

def create_key(name=None, email=None, key_type="rsa", key_bits=4096):
    """Generate a new SSH key with specified parameters."""
    # Ensure SSH directory exists
    ssh_dir = get_ssh_dir()
    ssh_dir.mkdir(exist_ok=True, parents=True)
    
    # Set default name if needed
    if not name:
        name = DEFAULT_ACCOUNT_NAME
    
    # Set default email if needed
    if not email:
        email = f"{name}@{DEFAULT_EMAIL_DOMAIN}"
    
    key_path = get_key_path(name)
    
    # Build ssh-keygen command
    cmd = [
        "ssh-keygen",
        "-o",                  # Use new OpenSSH format
        "-t", key_type,        # Key type (rsa)
        "-b", str(key_bits),   # Key bits (4096)
        "-C", email,           # Comment (email)
        "-f", str(key_path),   # Key file path
        "-N", ""               # No passphrase
    ]
    
    print(f"Generating {key_bits}-bit {key_type} key for {email}...")
    print(f"Key will be saved to: {key_path}")
    
    result = run_command(cmd)
    
    if result["success"]:
        return {
            "success": True,
            "key_path": key_path,
            "public_key_path": str(key_path) + ".pub",
            "message": f"Successfully generated key: {key_path}"
        }
    else:
        return {
            "success": False,
            "error": result.get("stderr", "Unknown error during key generation")
        }


def agent_operation(operation, key_path=None):
    """Manage SSH agent operations."""
    if operation == "start":
        # Different commands for different platforms
        if platform.system() == "Windows":
            # For Windows: first try to stop, then start
            print("Restarting SSH agent on Windows...")
            stop_cmd = ["powershell", "-Command", "Stop-Service ssh-agent -ErrorAction SilentlyContinue"]
            run_command(stop_cmd)
            
            start_cmd = ["powershell", "-Command", "Start-Service ssh-agent"]
            result = run_command(start_cmd)
            
            # Alternative approach if service command fails
            if not result["success"]:
                print("Using ssh-agent directly...")
                result = run_command(["ssh-agent", "-s"])
        else:
            # For Unix systems: kill and restart
            print("Restarting SSH agent...")
            run_command(["ssh-agent", "-k"])  # Kill agent
            result = run_command(["ssh-agent", "-s"])  # Start agent
        
        if result["success"]:
            return {
                "success": True, 
                "message": "SSH agent started successfully"
            }
        else:
            return {
                "success": False, 
                "error": result.get("stderr", "Failed to start SSH agent")
            }
            
    elif operation == "list":
        print("Listing keys in SSH agent...")
        result = run_command(["ssh-add", "-l"])
        
        if result["returncode"] == 0:
            if not result["stdout"].strip():
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": True, "keys": result["stdout"]}
        else:
            # Return code 1 on "no keys" is normal
            if "The agent has no identities" in result["stderr"]:
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": False, "error": result["stderr"]}
            
    elif operation == "add":
        if not key_path:
            key_path = get_key_path()
        
        print(f"Adding key to SSH agent: {key_path}")
        result = run_command(["ssh-add", str(key_path)])
        
        if result["success"]:
            return {
                "success": True, 
                "message": f"Added key: {key_path}"
            }
        else:
            return {
                "success": False, 
                "error": result.get("stderr", f"Failed to add key: {key_path}")
            }
            
    elif operation == "clear":
        print("Clearing all keys from SSH agent...")
        result = run_command(["ssh-add", "-D"])
        
        if result["success"]:
            return {
                "success": True, 
                "message": "Cleared all keys from agent"
            }
        else:
            return {
                "success": False, 
                "error": result.get("stderr", "Failed to clear keys")
            }
    
    return {
        "success": False, 
        "error": f"Unknown operation: {operation}"
    }


def git_operation(operation, param=None, target_dir=None):
    """Handle Git-related operations."""
    if operation == "test":
        service = param or "gitlab.com"
        print(f"Testing connection to {service}...")
        cmd = ["ssh", "-T", f"git@{service}"]
        result = run_command(cmd)
        
        # Note: ssh -T typically returns exit code 1 even on success
        # We need to check stderr for specific successful connection messages
        success_messages = [
            "successfully authenticated",
            "You've successfully authenticated",
            "Hi ",  # GitHub's greeting
            "Welcome to GitLab"  # GitLab's greeting
        ]
        
        stderr = result.get("stderr", "")
        stdout = result.get("stdout", "")
        output = stderr + stdout
        
        success = any(msg in output for msg in success_messages)
        
        if success:
            return {
                "success": True, 
                "message": f"Successfully connected to {service}",
                "details": output
            }
        else:
            return {
                "success": False, 
                "error": output or "Connection failed"
            }
            
    elif operation == "clone":
        if not param:
            return {
                "success": False, 
                "error": "No repository URL provided"
            }
            
        print(f"Cloning repository: {param}")
        cmd = ["git", "clone", param]
        if target_dir:
            cmd.append(target_dir)
            
        result = run_command(cmd)
        
        if result["success"]:
            return {
                "success": True, 
                "message": f"Successfully cloned repository"
            }
        else:
            return {
                "success": False, 
                "error": result.get("stderr", "Failed to clone repository")
            }
            
    elif operation == "remote":
        if not param:
            return {
                "success": False, 
                "error": "No remote URL provided"
            }
            
        print(f"Adding remote origin: {param}")
        cmd = ["git", "remote", "add", "origin", param]
        result = run_command(cmd)
        
        if result["success"]:
            # Verify the remote was added
            verify = run_command(["git", "remote", "-v"])
            return {
                "success": True, 
                "message": f"Remote added successfully", 
                "remotes": verify.get("stdout", "")
            }
        else:
            return {
                "success": False, 
                "error": result.get("stderr", "Failed to add remote")
            }
    
    return {
        "success": False, 
        "error": f"Unknown operation: {operation}"
    }


def show_key(name=None, copy_to_clipboard_flag=False):
    """Display the content of a public key and optionally copy it to clipboard."""
    key_path = get_key_path(name).with_suffix(".pub")
    
    if not key_path.exists():
        return {
            "success": False, 
            "error": f"Key not found: {key_path}"
        }
    
    print(f"Reading public key: {key_path}")
    try:
        with open(key_path, 'r') as f:
            key_content = f.read().strip()
        
        clipboard_msg = ""
        if copy_to_clipboard_flag:
            if copy_to_clipboard(key_content):
                clipboard_msg = "Key copied to clipboard"
            else:
                clipboard_msg = "Failed to copy to clipboard"
        
        return {
            "success": True,
            "key": key_content,
            "key_path": str(key_path),
            "clipboard_msg": clipboard_msg
        }
    except Exception as e:
        return {
            "success": False, 
            "error": f"Error reading key: {str(e)}"
        }


# ====== INTERACTIVE MODE ======

def interactive_session():
    """Run the SSH key manager in interactive mode."""
    print("\n===== SSH Key Manager =====\n")
    
    while True:
        print("\nWhat would you like to do?")
        print("1. Create new SSH key")
        print("2. Manage SSH agent")
        print("3. Work with Git repository")
        print("4. View/copy existing key")
        print("5. Exit")
        
        choice = input("\n> ")
        
        if choice == "1":
            # Create key flow
            print("\n--- Create SSH Key ---")
            name = input(f"Key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
            email = input(f"Email [{name or DEFAULT_ACCOUNT_NAME}@{DEFAULT_EMAIL_DOMAIN}]: ").strip() or None
            
            result = create_key(name=name, email=email)
            
            if result["success"]:
                print(f"\n✓ {result['message']}")
                
                # Auto-add to agent
                add_result = agent_operation("add", result["key_path"])
                if add_result["success"]:
                    print(f"✓ Key automatically added to SSH agent")
                
                # Show the key
                key_result = show_key(name)
                if key_result["success"]:
                    print("\nYour public key:")
                    print(key_result["key"])
                    print("\nAdd this key to your Git service (GitLab/GitHub)")
                    print("GitLab: https://gitlab.com/-/profile/keys")
                    print("GitHub: https://github.com/settings/keys")
            else:
                print(f"\n✗ Error: {result['error']}")
        
        elif choice == "2":
            # Agent management flow
            print("\n--- SSH Agent Management ---")
            print("1. Start/restart agent")
            print("2. List loaded keys")
            print("3. Add key to agent")
            print("4. Clear all keys")
            print("5. Back")
            
            agent_choice = input("\n> ")
            
            if agent_choice == "1":
                result = agent_operation("start")
                print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
                
            elif agent_choice == "2":
                result = agent_operation("list")
                print("\nLoaded keys:")
                if "keys" in result:
                    print(result["keys"])
                else:
                    print(result.get("message", "No keys loaded or error checking"))
                
            elif agent_choice == "3":
                name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
                key_path = get_key_path(name)
                result = agent_operation("add", key_path)
                print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
                
            elif agent_choice == "4":
                result = agent_operation("clear")
                print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
        
        elif choice == "3":
            # Git operations flow
            print("\n--- Git Operations ---")
            print("1. Test connection")
            print("2. Clone repository")
            print("3. Add remote")
            print("4. Back")
            
            git_choice = input("\n> ")
            
            if git_choice == "1":
                service = input("Enter Git service [gitlab.com]: ").strip() or "gitlab.com"
                result = git_operation("test", service)
                print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
                if "details" in result:
                    print(f"Details: {result['details']}")
                
            elif git_choice == "2":
                repo_url = input("Enter repository URL: ").strip()
                target_dir = input("Enter target directory [default]: ").strip() or None
                if repo_url:
                    result = git_operation("clone", repo_url, target_dir)
                    print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
                else:
                    print("\n✗ No repository URL provided")
                    
            elif git_choice == "3":
                remote_url = input("Enter remote URL: ").strip()
                if remote_url:
                    result = git_operation("remote", remote_url)
                    print(f"\n{'✓' if result['success'] else '✗'} {result.get('message', result.get('error', ''))}")
                    if result["success"] and "remotes" in result:
                        print("\nConfigured remotes:")
                        print(result["remotes"])
                else:
                    print("\n✗ No remote URL provided")
        
        elif choice == "4":
            # Show key flow
            print("\n--- View/Copy SSH Key ---")
            name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
            copy = input("Copy to clipboard? (y/n) [n]: ").lower().startswith("y")
            
            result = show_key(name, copy)
            
            if result["success"]:
                print(f"\nPublic key ({result['key_path']}):")
                print(result["key"])
                
                if result.get("clipboard_msg", ""):
                    print(f"\n✓ {result['clipboard_msg']}")
                    
                print("\nAdd this key to your Git service:")
                print("GitLab: https://gitlab.com/-/profile/keys")
                print("GitHub: https://github.com/settings/keys")
            else:
                print(f"\n✗ Error: {result['error']}")
        
        elif choice == "5":
            print("\nExiting SSH Key Manager")
            return
        
        else:
            print("\n✗ Invalid choice. Please try again.")


# ====== COMMAND LINE INTERFACE ======

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SSH Key Manager - Generate and manage SSH keys for Git services"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Create key command
    create_parser = subparsers.add_parser("create", help="Generate a new SSH key")
    create_parser.add_argument("--name", help=f"Key name (defaults to {DEFAULT_ACCOUNT_NAME})")
    create_parser.add_argument("--email", help=f"Email for key comment (defaults to name@{DEFAULT_EMAIL_DOMAIN})")
    create_parser.add_argument("--type", default="rsa", help="Key type (default: rsa)")
    create_parser.add_argument("--bits", type=int, default=4096, help="Key bits (default: 4096)")
    
    # Agent command
    agent_parser = subparsers.add_parser("agent", help="Manage SSH agent")
    agent_parser.add_argument(
        "operation",
        choices=["start", "list", "add", "clear"],
        help="Agent operation"
    )
    agent_parser.add_argument("key", nargs="?", help="Key to add (for 'add' operation)")
    
    # Git command
    git_parser = subparsers.add_parser("git", help="Git operations")
    git_parser.add_argument(
        "operation",
        choices=["test", "clone", "remote"],
        help="Git operation"
    )
    git_parser.add_argument("param", help="Service name, repo URL or remote URL")
    git_parser.add_argument("dir", nargs="?", help="Target directory for clone")
    
    # Show key command
    show_parser = subparsers.add_parser("show", help="Show public key")
    show_parser.add_argument("--name", help=f"Key name (defaults to {DEFAULT_ACCOUNT_NAME})")
    show_parser.add_argument("--copy", action="store_true", help="Copy key to clipboard")
    
    return parser.parse_args()


# ====== MAIN ENTRY POINT ======

def main():
    """Main entry point for the SSH Key Manager."""
    # Check if ssh-keygen is available
    if not shutil.which("ssh-keygen"):
        print("ERROR: ssh-keygen not found. Please install SSH.")
        sys.exit(1)
    
    # If no arguments, run interactive mode
    if len(sys.argv) == 1:
        interactive_session()
        return
    
    # Parse command line arguments
    args = parse_args()
    
    # Handle commands
    if args.command == "create":
        result = create_key(
            name=args.name,
            email=args.email,
            key_type=args.type,
            key_bits=args.bits
        )
        
        if result["success"]:
            print(f"✓ {result['message']}")
            
            # Automatically add to agent
            add_result = agent_operation("add", result["key_path"])
            if add_result["success"]:
                print(f"✓ Key added to SSH agent")
                
            # Show the key
            key_result = show_key(args.name)
            if key_result["success"]:
                print("\nYour public key:")
                print(key_result["key"])
                print("\nAdd this key to your Git service:")
                print("GitLab: https://gitlab.com/-/profile/keys")
                print("GitHub: https://github.com/settings/keys")
            
        else:
            print(f"✗ Error: {result['error']}")
    
    elif args.command == "agent":
        if args.operation == "add" and args.key:
            # Key specified directly
            result = agent_operation(args.operation, args.key)
        elif args.operation == "add":
            # No key specified, use default
            result = agent_operation(args.operation, get_key_path(None))
        else:
            result = agent_operation(args.operation)
            
        if result["success"]:
            if args.operation == "list" and "keys" in result:
                print("Loaded keys:")
                print(result["keys"])
            else:
                print(f"✓ {result['message']}")
        else:
            print(f"✗ Error: {result['error']}")
    
    elif args.command == "git":
        result = git_operation(args.operation, args.param, args.dir)
        
        if result["success"]:
            print(f"✓ {result['message']}")
            if "remotes" in result:
                print("\nConfigured remotes:")
                print(result["remotes"])
        else:
            print(f"✗ Error: {result['error']}")
    
    elif args.command == "show":
        result = show_key(args.name, args.copy)
        
        if result["success"]:
            print(f"Public key ({result['key_path']}):")
            print(result["key"])
            
            if result.get("clipboard_msg", ""):
                print(f"\n✓ {result['clipboard_msg']}")
                
            print("\nAdd this key to your Git service:")
            print("GitLab: https://gitlab.com/-/profile/keys")
            print("GitHub: https://github.com/settings/keys")
        else:
            print(f"✗ Error: {result['error']}")


if __name__ == "__main__":
    main()
