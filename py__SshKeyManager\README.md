# SSH Key Manager

Minimalist SSH key generation and management utility with automatic OpenSSH setup for Windows.

## Features

- **Clean Interactive CLI** - Intuitive menu-driven interface
- **Zero Python dependencies** - Uses only standard library
- **Automatic OpenSSH setup** - Detects and fixes missing dependencies
- **Cross-platform support** - Windows, macOS, Linux
- **SSH agent management** - Start/stop, add/remove keys
- **Git integration** - Test connections, clone repos, manage remotes
- **Modern uv support** - Fast, reliable dependency management
- **Key management** - Generate, view, copy SSH keys

## Quick Start

### Using uv (Recommended)

```bash
# Install uv if not already installed
pip install uv

# Setup project
uv venv
uv pip install -e .

# Run
uv run python src\main.py
```

### Using Python directly

```bash
# No dependencies to install!
python src\main.py
# Or use the existing batch file
src\main.bat
```

### Using the launcher (Windows)

```bash
# Uses uv if available, falls back to python
.\run.bat
```

## Project Structure

```
py__SshKeyManager/
├── src/
│   ├── main.py              # Main application
│   └── main.bat             # Windows launcher
├── run.bat                  # uv-aware launcher
├── install_uv.ps1          # uv installation script
├── pyproject.toml          # Modern Python project config
├── requirements.txt        # Zero dependencies
├── uv.lock                # uv lock file
├── SshKeyManager.code-workspace  # VSCode workspace
└── README.md              # This file
```

## Why uv?

- **10-100x faster** than pip
- **Better dependency resolution**
- **Lock files** for reproducible environments
- **Modern tooling** built with Rust
- **Drop-in replacement** for pip

## uv Commands

```bash
uv venv                    # Create virtual environment
uv pip install -e .       # Install project in development mode
uv add --dev pytest       # Add development dependency
uv run python src\main.py  # Run with uv
uv sync                   # Sync environment with lock file
```

## Development

```bash
# Install development dependencies
uv pip install -e ".[dev]"

# Format code
uv run black src\main.py

# Lint code
uv run flake8 src\main.py

# Run tests (when available)
uv run pytest
```

## Architecture

The project follows minimalist principles:
- Single main.py file with all functionality
- Zero external dependencies
- Automatic dependency detection and installation
- Clean, readable code structure
- Respect for existing code patterns
- uv integration for modern dependency management
- Backward compatibility with existing batch files
