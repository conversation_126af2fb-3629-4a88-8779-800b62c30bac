# Dir `py__SshKeyManager`

*Files marked `[-]` are shown in structure but not included in content.*

### File Structure

```
├── .gitignore
├── README.md
├── SshKeyManager.code-workspace
├── SshKeyManager.sublime-project
├── SshKeyManager.sublime-workspace [-]
├── install_uv.ps1
├── py_venv_init.bat
├── pyproject.toml
├── requirements.txt
├── run.bat
├── understanding_the_environment.md
├── uv.lock
└── src
    ├── .gitignore
    ├── code_guidelines.md
    └── main.py
```

---

#### `.gitignore`

    # Parent/Workspace .gitignore
    # For managing files in a general development/notes parent directory.
    # This is intended for a higher-level folder that might contain multiple projects.
    
    # =======================================================
    # DIRECTORIES
    # =======================================================
    
    # python
    **/.cache/
    **/.env/
    **/.pytest_cache/
    **/.venv/
    **/__pycache__/
    **/env/
    **/venv/
    
    # nodejs/react
    **/node_modules/
    
    # build and temp
    **/build/
    **/cache/
    **/dist/
    **/logs/
    **/temp/
    **/tmp/
    
    # =======================================================
    # EXTENSIONS
    # =======================================================
    
    # media
    *.mp4
    *.mkv
    *.webm
    *.mp3
    *.wav
    *.png
    
    # ide
    *.sublime-workspace
    *.sublime_session
    *.swp
    
    # binaries
    *.bin
    *.dll
    *.exe
    *.pyc
    *.pyo
    
    # misc
    *.DS_Store
    *.blend1
    *.ini.bak
    *.ldb
    *.log
    *.pak
    *.pickle
    *.prv.ppk
    *.prv.pub
    *.tmp
    
    # =======================================================
    # EXPLICIT
    # =======================================================
    
    # filenames
    **/.what-is-this.md
    **/app.log.yml
    **/quit.blend
    **/Run History-1.5a.csv
    **/Search History-1.5a.csv
    **/Session-1.5a.backup.json
    **/Session-1.5a.json
    
    # paths
    **/*sync-conflict*.*
    **/user-data/**/ui_messages.json
    **/.specstory/history/.what-is-this.md

---

#### `README.md`

```markdown
    # SSH Key Manager
    
    Minimalist SSH key generation and management utility with automatic OpenSSH setup for Windows.
    
    ## Features
    
    - **Clean Interactive CLI** - Intuitive menu-driven interface
    - **Zero Python dependencies** - Uses only standard library
    - **Automatic OpenSSH setup** - Detects and fixes missing dependencies
    - **Cross-platform support** - Windows, macOS, Linux
    - **SSH agent management** - Start/stop, add/remove keys
    - **Git integration** - Test connections, clone repos, manage remotes
    - **Modern uv support** - Fast, reliable dependency management
    - **Key management** - Generate, view, copy SSH keys
    
    ## Quick Start
    
    ### Using uv (Recommended)
    
    ```bash
    # Install uv if not already installed
    pip install uv
    
    # Setup project
    uv venv
    uv pip install -e .
    
    # Run
    uv run python src\main.py
    ```
    
    ### Using Python directly
    
    ```bash
    # No dependencies to install!
    python src\main.py
    # Or use the existing batch file
    src\main.bat
    ```
    
    ### Using the launcher (Windows)
    
    ```bash
    # Uses uv if available, falls back to python
    .\run.bat
    ```
    
    ## Project Structure
    
    ```
    py__SshKeyManager/
    ├── src/
    │   ├── main.py              # Main application
    │   └── main.bat             # Windows launcher
    ├── run.bat                  # uv-aware launcher
    ├── install_uv.ps1          # uv installation script
    ├── pyproject.toml          # Modern Python project config
    ├── requirements.txt        # Zero dependencies
    ├── uv.lock                # uv lock file
    ├── SshKeyManager.code-workspace  # VSCode workspace
    └── README.md              # This file
    ```
    
    ## Why uv?
    
    - **10-100x faster** than pip
    - **Better dependency resolution**
    - **Lock files** for reproducible environments
    - **Modern tooling** built with Rust
    - **Drop-in replacement** for pip
    
    ## uv Commands
    
    ```bash
    uv venv                    # Create virtual environment
    uv pip install -e .       # Install project in development mode
    uv add --dev pytest       # Add development dependency
    uv run python src\main.py  # Run with uv
    uv sync                   # Sync environment with lock file
    ```
    
    ## Development
    
    ```bash
    # Install development dependencies
    uv pip install -e ".[dev]"
    
    # Format code
    uv run black src\main.py
    
    # Lint code
    uv run flake8 src\main.py
    
    # Run tests (when available)
    uv run pytest
    ```
    
    ## Architecture
    
    The project follows minimalist principles:
    - Single main.py file with all functionality
    - Zero external dependencies
    - Automatic dependency detection and installation
    - Clean, readable code structure
    - Respect for existing code patterns
    - uv integration for modern dependency management
    - Backward compatibility with existing batch files
```

---

#### `SshKeyManager.code-workspace`

```code-workspace
    {
        "folders": [
            // [ROOT]
            {
                // [DEFAULT]
                // =======================================================
                "name": "py__SshKeyManager",
                "path": "."
            }
        ],
        "settings": {
            // Python Configuration
            "python.defaultInterpreterPath": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\python.exe",
            "python.terminal.activateEnvironment": true,
            "python.terminal.activateEnvInCurrentTerminal": true,
    
            // // Formatting
            // "python.formatting.provider": "black",
            // "python.formatting.blackArgs": ["--line-length=88"],
            // "editor.formatOnSave": false,
            // "python.formatting.autopep8Args": ["--max-line-length=88"],
    
            // // Linting
            // "python.linting.enabled": true,
            // "python.linting.flake8Enabled": true,
            // "python.linting.pylintEnabled": false,
            // "python.linting.flake8Args": ["--max-line-length=88"],
    
            // // Testing
            // "python.testing.pytestEnabled": true,
            // "python.testing.unittestEnabled": false,
            // "python.testing.pytestArgs": ["."],
    
            // Editor Settings
            "editor.tabSize": 4,
            "editor.insertSpaces": true,
            "editor.detectIndentation": false,
            "files.eol": "\n",
            "files.insertFinalNewline": true,
            "files.trimTrailingWhitespace": true,
    
            // File Associations
            "files.associations": {
                "*.jinja-*": "jinja",
                "*.jinja2": "jinja",
                "requirements*.txt": "pip-requirements"
            },
    
            // Exclude patterns
            "files.exclude": {
                "**/__pycache__": true,
                "**/*.pyc": true,
                "**/*.pyo": true,
                "**/venv": true,
                "**/.git": true,
                "**/*.egg-info": true,
                "**/.pytest_cache": true,
                "**/logs": true,
                "**/*.log": true
            },
    
            // Search exclude patterns
            "search.exclude": {
                "**/venv": true,
                "**/__pycache__": true,
                "**/*.pyc": true,
                "**/logs": true,
                "**/.git": true
            },
    
            // Terminal
            "terminal.integrated.defaultProfile.windows": "PowerShell",
            "terminal.integrated.env.windows": {
                "PYTHONPATH": "${workspaceFolder}/src"
            },
    
            // IntelliSense
            "python.analysis.autoImportCompletions": true,
            "python.analysis.typeCheckingMode": "basic",
            "python.analysis.autoSearchPaths": true,
            "python.analysis.extraPaths": ["./src"],
    
            // Git
            "git.ignoreLimitWarning": true,
    
            // // Workspace specific
            // "workbench.colorTheme": "Default Dark+",
            // "explorer.confirmDelete": false,
            // "explorer.confirmDragAndDrop": false
        },
        "extensions": {
            "recommendations": [
                // "eamodio.gitlens",
                // "ms-python.black-formatter",
                "ms-python.debugpy",
                "ms-python.flake8",
                "ms-python.isort",
                "ms-python.python",
                "ms-vscode.batch",
                "ms-vscode.powershell",
                "ms-vscode.vscode-json",
                // "redhat.vscode-yaml",
                // "wholroyd.jinja",
            ]
        },
        "launch": {
            "version": "0.2.0",
            "configurations": [
                {
                    "name": "Python: Current File",
                    "type": "python",
                    "request": "launch",
                    "program": "${file}",
                    "console": "integratedTerminal",
                    "cwd": "${workspaceFolder}",
                    "env": {
                        "PYTHONPATH": "${workspaceFolder}/src"
                    },
                    "justMyCode": true
                },
                {
                    "name": "Python: Main Module (py__SshKeyManager)",
                    "type": "python",
                    "request": "launch",
                    "program": "${workspaceFolder}/src/main.py",
                    "console": "integratedTerminal",
                    "cwd": "${workspaceFolder}",
                    "env": {
                        "PYTHONPATH": "${workspaceFolder}/src"
                    },
                    "justMyCode": true
                },
                // {
                //     "name": "Python: Debug Tests",
                //     "type": "python",
                //     "request": "launch",
                //     "module": "pytest",
                //     "args": ["-v"],
                //     "console": "integratedTerminal",
                //     "cwd": "${workspaceFolder}",
                //     "env": {
                //         "PYTHONPATH": "${workspaceFolder}/src"
                //     },
                //     "justMyCode": false
                // }
            ]
        },
        "tasks": {
            "version": "2.0.0",
            "tasks": [
                {
                    "label": "Python: Run Current File",
                    "type": "shell",
                    "command": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\python.exe",
                    "args": ["-u", "${file}"],
                    "group": {
                        "kind": "build",
                        "isDefault": true
                    },
                    "presentation": {
                        "echo": true,
                        "reveal": "always",
                        "focus": false,
                        "panel": "shared"
                    },
                    "options": {
                        "cwd": "${workspaceFolder}",
                        "env": {
                            "PYTHONPATH": "${workspaceFolder}/src"
                        }
                    },
                    "problemMatcher": []
                },
                {
                    "label": "Python: Install Requirements",
                    "type": "shell",
                    "command": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\pip.exe",
                    "args": ["install", "-r", "requirements.txt"],
                    "group": "build",
                    "presentation": {
                        "echo": true,
                        "reveal": "always",
                        "focus": false,
                        "panel": "shared"
                    },
                    "options": {
                        "cwd": "${workspaceFolder}"
                    },
                    "problemMatcher": []
                },
                {
                    "label": "Python: Run Tests",
                    "type": "shell",
                    "command": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\python.exe",
                    "args": ["-m", "pytest", "-v"],
                    "group": "test",
                    "presentation": {
                        "echo": true,
                        "reveal": "always",
                        "focus": false,
                        "panel": "shared"
                    },
                    "options": {
                        "cwd": "${workspaceFolder}",
                        "env": {
                            "PYTHONPATH": "${workspaceFolder}/src"
                        }
                    },
                    "problemMatcher": []
                },
                // {
                //     "label": "Python: Format with Black",
                //     "type": "shell",
                //     "command": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\black.exe",
                //     "args": ["--line-length=88", "${workspaceFolder}/src"],
                //     "group": "build",
                //     "presentation": {
                //         "echo": true,
                //         "reveal": "always",
                //         "focus": false,
                //         "panel": "shared"
                //     },
                //     "options": {
                //         "cwd": "${workspaceFolder}"
                //     },
                //     "problemMatcher": []
                // }
            ]
        }
    }
```

---

#### `SshKeyManager.sublime-project`

```sublime-project
    {
        "folders": [
            {
                "path": ".",
                "folder_exclude_patterns": [".stfolder", ".git", "__pycache__", "venv", "*.egg-info", ],
                "file_exclude_patterns": ["*.sublime-workspace", "*.pyc", "*.pyo", "*.swp", "*.tmp", ".DS_Store", ],
            },
    
        ],
        "settings": {
            "tab_size": 4,
            "default_line_ending": "unix",
            "translate_tabs_to_spaces": true,
            "ensure_newline_at_eof_on_save": true,
            "trim_trailing_white_space_on_save": true,
            "python_interpreter": "$project_path\\venv\\Scripts\\python",
            // "python_interpreter": "\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
            "python_formatter": "black",
            "python_linter": "flake8",
            "python_format_on_save": false
        },
        "build_systems": [
            // [py:build2]
            // =======================================================
            {
                "name": "[py:build2] \t SshKeyManager.sublime-project",
                "cmd": [
                    "$project_path\\venv\\Scripts\\python",
                    // "\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
                    "-u",
                    "${file}"
                ],
                "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                "selector": "source.python",
                "shell": true,
                // "syntax": "Packages/build2/buildfile.sublime-syntax",
                // "syntax": "JSON.sublime-syntax",
                // "syntax": "Python.sublime-syntax",
                "syntax": "Markdown.sublime-syntax",
                "working_dir": "${project_path}"
            },
    
            // [py:terminus:open]
            // =======================================================
            {
                "name": "[py:terminus_open] \t SshKeyManager.sublime-project",
                "target": "terminus_open",
                "auto_close": false,
                "title": "py.output",
                "focus": true,
                "timeit": true,
                "post_window_hooks": [["carry_file_to_pane", {"direction": "right"}]],
                "shell_cmd": "\"$project_path\\venv\\Scripts\\python\" -u \"${file}\"",
                "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                "selector": "source.python",
                "env": {"PYTHONIOENCODING": "utf-8"},
                "working_dir": "${project_path}",
            },
    
            // [py:terminus:panel]
            // =======================================================
            {
                "name": "[py:terminus_panel] \t SshKeyManager.sublime-project",
                "target": "toggle_terminus_panel",
                "cancel": "terminus_cancel_build",
                "auto_close": false,
                "focus": true,
                "timeit": true,
                "shell_cmd": "\"$project_path\\venv\\Scripts\\python\" -u \"${file}\"",
                "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                "selector": "source.python",
                "env": {"PYTHONIOENCODING": "utf-8"},
                "working_dir": "${project_path}",
            },
    
            // [batch:terminus:open]
            // =======================================================
            {
                "name": "[bat:terminus_open] \t SshKeyManager.sublime-project",
                "target": "terminus_open",
                "auto_close": false,
                "focus": true,
                "timeit": true,
                "cmd": ["cmd", "/c", "${file}"],
                "selector": "source.Batch",
                "working_dir": "${project_path}",
            },
        ]
    }
```

---

#### `install_uv.ps1`

```ps1
    powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

---

#### `py_venv_init.bat`

```batch
    :: =============================================================================
    :: Understanding the Environment
    :: =============================================================================
    ::
    :: This project uses a structured batch script approach for Python environment management:
    ::
    :: 1. `py_venv_init.bat` - Creates and initializes a Python virtual environment
    :: 2. `main.bat` - Runs the main Python application after activating the virtual environment
    :: 3. `requirements.txt` - Should contain dependencies (appears to be empty currently)
    ::
    :: Environment Setup Plan
    :: 1. **Initialize the Virtual Environment**:
    ::    - We'll execute `py_venv_init.bat` from the current directory
    ::    - This script will:
    ::      - Find available Python installations
    ::      - Prompt to select a version if multiple are found
    ::      - Create a virtual environment named "venv"
    ::      - Install/upgrade pip
    ::      - Install any packages listed in requirements.txt
    ::      - Ask if you want to upgrade packages
    :: 2. **Running the Application**:
    ::    - After environment setup, we'll use `main.bat` to run the application
    ::    - This will:
    ::      - Locate and activate the virtual environment
    ::      - Execute `main.py` with the `--prompt` flag
    ::
    :: Command Execution
    ::    - For Windows 11, we'll use Command Prompt or PowerShell to execute the batch files. The correct commands would be:
    ::
    :: ```
    :: # For initializing the environment:
    :: .\py_venv_init.bat
    :: # After initialization, to run the application:
    :: .\main.bat
    :: ```
    
    
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    TITLE (py) %~nx0
    
    
    :: =============================================================================
    :: py: version
    :: =============================================================================
    SET /A "py_count=0"
    FOR /F "delims=" %%p IN ('WHERE python 2^>nul') DO (
        FOR /F "tokens=*" %%v IN ('"%%p" --version 2^>^&1 2^>nul') DO (
            ECHO %%v | FIND "Python " > NUL && (
                SET /A "py_count+=1"
                SET "py_version!py_count!=%%v"
                SET "python_!py_count!=%%p"
            )
        )
    )
    IF %py_count% EQU 0 (
        ECHO No valid Python installations found. & ECHO. & PAUSE>NUL & EXIT /B
    ) ELSE IF %py_count% EQU 1 (SET "py_path=%python_1%") ELSE (
        ECHO Choose Version: & FOR /L %%i IN (1,1,%py_count%) DO (
            ECHO - [%%i] !py_version%%i! - !python_%%i!
        )
        ECHO. & SET /P version="Choose the Python version to use (enter number): "
        CALL SET "py_path=%%python_!version!%%" & ECHO.
    )
    
    
    :: =============================================================================
    :: venv: create/initialize/activate
    :: =============================================================================
    SET "__venv_stem__=%__init_path__%"
    IF NOT EXIST "%__venv_stem__%\Scripts\python.exe" ("!py_path!" -m venv "%__venv_name__%")
    CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
    
    
    :: :: =============================================================================
    :: :: .gitignore: write
    :: :: =============================================================================
    :: SETLOCAL DISABLEDELAYEDEXPANSION
    :: ECHO * > "%__venv_stem__%\%__venv_name__%\.gitignore"
    :: ECHO !.gitignore >> "%__venv_stem__%\%__venv_name__%\.gitignore"
    :: ECHO * > "%__venv_stem__%\%__venv_name__%\.stignore"
    :: ECHO !.stignore >> "%__venv_stem__%\%__venv_name__%\.stignore"
    :: ENDLOCAL
    
    
    :: =============================================================================
    :: requirements.txt: install
    :: =============================================================================
    SET "requirements_txt=%__venv_stem__%\requirements.txt"
    IF EXIST "%requirements_txt%" (
        "python" -m pip install --upgrade pip
        "python" -m pip install -r "%requirements_txt%"
        IF NOT ERRORLEVEL 1 ("pip" freeze > "%requirements_txt%") ELSE (
            ECHO Failed to install packages from "%requirements_txt%"
        )
    )
    
    
    :: =============================================================================
    :: requirements.txt: upgrade
    :: =============================================================================
    IF EXIST "%requirements_txt%" (
        ECHO. & ECHO Do you want to upgrade the packages? ^(press 'y' to upgrade...^)
        SET /P upgrade_packages=">> "
        IF "!upgrade_packages!"=="y" (
            "python" -m pip install --upgrade -r "%requirements_txt%"
            IF NOT ERRORLEVEL 1 ("pip" freeze > "%requirements_txt%") ELSE (
                ECHO Failed to upgrade packages from "%requirements_txt%"
            )
        )
    )
    
    
    :: =============================================================================
    :: requirements.txt: write
    :: =============================================================================
    SET "requirements_txt=%__venv_stem__%\requirements.txt"
    IF NOT ERRORLEVEL 1 ("pip" freeze > %requirements_txt%) ELSE (
        ECHO Failed to write  %requirements_txt%
    )
    
    
    :: =============================================================================
    :: cmd: exit
    :: =============================================================================
    ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
    EXIT /B
```

---

#### `pyproject.toml`

```toml
    [build-system]
    requires = ["hatchling"]
    build-backend = "hatchling.build"
    
    [project]
    name = "ssh-key-manager"
    version = "1.0.0"
    description = "SSH key management tool with automatic OpenSSH setup"
    requires-python = ">=3.8"
    dependencies = []
    
    [project.optional-dependencies]
    dev = ["pytest>=7.0.0", "black>=22.0.0", "flake8>=4.0.0"]
    
    [project.scripts]
    ssh-key-manager = "src.main:main"
    
    [tool.uv]
    dev-dependencies = ["pytest>=7.0.0", "black>=22.0.0", "flake8>=4.0.0"]
    
    [tool.hatch.build.targets.wheel]
    packages = ["src"]
    
    [tool.black]
    line-length = 79
    target-version = ['py38']
```

---

#### `requirements.txt`

```text
    # SSH Key Manager - Zero Dependencies
    # Uses only Python standard library modules
```

---

#### `run.bat`

```batch
    @echo off
    :: SSH Key Manager - Simple launcher
    :: Uses uv if available, falls back to existing batch files
    
    where uv >nul 2>&1
    if %errorlevel% == 0 (
        echo Using uv...
        uv run python src\main.py %*
    ) else (
        echo Using existing launcher...
        call src\main.bat %*
    )
```

---

#### `understanding_the_environment.md`

```markdown
    ## Understanding the Environment
    
    This project uses a structured batch script approach for Python environment management:
    
    1. `py_venv_init.bat` - Creates and initializes a Python virtual environment
    2. `main.bat` - Runs the main Python application after activating the virtual environment
    3. `requirements.txt` - Should contain dependencies (appears to be empty currently)
    
    ## Environment Setup Plan
    
    1. **Initialize the Virtual Environment**:
       - We'll execute `py_venv_init.bat` from the current directory
       - This script will:
         - Find available Python installations
         - Prompt to select a version if multiple are found
         - Create a virtual environment named "venv"
         - Install/upgrade pip
         - Install any packages listed in requirements.txt
         - Ask if you want to upgrade packages
    
    2. **Running the Application**:
       - After environment setup, we'll use `main.bat` to run the application
       - This will:
         - Locate and activate the virtual environment
         - Execute `main.py` (with the provided args)
    
    ## Command Execution
    
    For Windows 11, we'll use Command Prompt or PowerShell to execute the batch files. The correct commands would be:
    
    For initializing the environment: `./py_venv_init.bat`
    
    After initialization, to run the application: `./src/main.bat`
```

---

#### `uv.lock`

```lock
    version = 1
    revision = 2
    requires-python = ">=3.8"
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
        "python_full_version < '3.8.1'",
    ]
    
    [[package]]
    name = "black"
    version = "24.8.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
        "python_full_version < '3.8.1'",
    ]
    dependencies = [
        { name = "click", version = "8.1.8", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.9'" },
        { name = "mypy-extensions", marker = "python_full_version < '3.9'" },
        { name = "packaging", marker = "python_full_version < '3.9'" },
        { name = "pathspec", marker = "python_full_version < '3.9'" },
        { name = "platformdirs", version = "4.3.6", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.9'" },
        { name = "tomli", marker = "python_full_version < '3.9'" },
        { name = "typing-extensions", marker = "python_full_version < '3.9'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/04/b0/46fb0d4e00372f4a86a6f8efa3cb193c9f64863615e39010b1477e010578/black-24.8.0.tar.gz", hash = "sha256:2500945420b6784c38b9ee885af039f5e7471ef284ab03fa35ecdde4688cd83f", size = 644810, upload-time = "2024-08-02T17:43:18.405Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/47/6e/74e29edf1fba3887ed7066930a87f698ffdcd52c5dbc263eabb06061672d/black-24.8.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:09cdeb74d494ec023ded657f7092ba518e8cf78fa8386155e4a03fdcc44679e6", size = 1632092, upload-time = "2024-08-02T17:47:26.911Z" },
        { url = "https://files.pythonhosted.org/packages/ab/49/575cb6c3faee690b05c9d11ee2e8dba8fbd6d6c134496e644c1feb1b47da/black-24.8.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:81c6742da39f33b08e791da38410f32e27d632260e599df7245cccee2064afeb", size = 1457529, upload-time = "2024-08-02T17:47:29.109Z" },
        { url = "https://files.pythonhosted.org/packages/7a/b4/d34099e95c437b53d01c4aa37cf93944b233066eb034ccf7897fa4e5f286/black-24.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:707a1ca89221bc8a1a64fb5e15ef39cd755633daa672a9db7498d1c19de66a42", size = 1757443, upload-time = "2024-08-02T17:46:20.306Z" },
        { url = "https://files.pythonhosted.org/packages/87/a0/6d2e4175ef364b8c4b64f8441ba041ed65c63ea1db2720d61494ac711c15/black-24.8.0-cp310-cp310-win_amd64.whl", hash = "sha256:d6417535d99c37cee4091a2f24eb2b6d5ec42b144d50f1f2e436d9fe1916fe1a", size = 1418012, upload-time = "2024-08-02T17:47:20.33Z" },
        { url = "https://files.pythonhosted.org/packages/08/a6/0a3aa89de9c283556146dc6dbda20cd63a9c94160a6fbdebaf0918e4a3e1/black-24.8.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:fb6e2c0b86bbd43dee042e48059c9ad7830abd5c94b0bc518c0eeec57c3eddc1", size = 1615080, upload-time = "2024-08-02T17:48:05.467Z" },
        { url = "https://files.pythonhosted.org/packages/db/94/b803d810e14588bb297e565821a947c108390a079e21dbdcb9ab6956cd7a/black-24.8.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:837fd281f1908d0076844bc2b801ad2d369c78c45cf800cad7b61686051041af", size = 1438143, upload-time = "2024-08-02T17:47:30.247Z" },
        { url = "https://files.pythonhosted.org/packages/a5/b5/f485e1bbe31f768e2e5210f52ea3f432256201289fd1a3c0afda693776b0/black-24.8.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:62e8730977f0b77998029da7971fa896ceefa2c4c4933fcd593fa599ecbf97a4", size = 1738774, upload-time = "2024-08-02T17:46:17.837Z" },
        { url = "https://files.pythonhosted.org/packages/a8/69/a000fc3736f89d1bdc7f4a879f8aaf516fb03613bb51a0154070383d95d9/black-24.8.0-cp311-cp311-win_amd64.whl", hash = "sha256:72901b4913cbac8972ad911dc4098d5753704d1f3c56e44ae8dce99eecb0e3af", size = 1427503, upload-time = "2024-08-02T17:46:22.654Z" },
        { url = "https://files.pythonhosted.org/packages/a2/a8/05fb14195cfef32b7c8d4585a44b7499c2a4b205e1662c427b941ed87054/black-24.8.0-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:7c046c1d1eeb7aea9335da62472481d3bbf3fd986e093cffd35f4385c94ae368", size = 1646132, upload-time = "2024-08-02T17:49:52.843Z" },
        { url = "https://files.pythonhosted.org/packages/41/77/8d9ce42673e5cb9988f6df73c1c5c1d4e9e788053cccd7f5fb14ef100982/black-24.8.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:649f6d84ccbae73ab767e206772cc2d7a393a001070a4c814a546afd0d423aed", size = 1448665, upload-time = "2024-08-02T17:47:54.479Z" },
        { url = "https://files.pythonhosted.org/packages/cc/94/eff1ddad2ce1d3cc26c162b3693043c6b6b575f538f602f26fe846dfdc75/black-24.8.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:2b59b250fdba5f9a9cd9d0ece6e6d993d91ce877d121d161e4698af3eb9c1018", size = 1762458, upload-time = "2024-08-02T17:46:19.384Z" },
        { url = "https://files.pythonhosted.org/packages/28/ea/18b8d86a9ca19a6942e4e16759b2fa5fc02bbc0eb33c1b866fcd387640ab/black-24.8.0-cp312-cp312-win_amd64.whl", hash = "sha256:6e55d30d44bed36593c3163b9bc63bf58b3b30e4611e4d88a0c3c239930ed5b2", size = 1436109, upload-time = "2024-08-02T17:46:52.97Z" },
        { url = "https://files.pythonhosted.org/packages/9f/d4/ae03761ddecc1a37d7e743b89cccbcf3317479ff4b88cfd8818079f890d0/black-24.8.0-cp38-cp38-macosx_10_9_x86_64.whl", hash = "sha256:505289f17ceda596658ae81b61ebbe2d9b25aa78067035184ed0a9d855d18afd", size = 1617322, upload-time = "2024-08-02T17:51:20.203Z" },
        { url = "https://files.pythonhosted.org/packages/14/4b/4dfe67eed7f9b1ddca2ec8e4418ea74f0d1dc84d36ea874d618ffa1af7d4/black-24.8.0-cp38-cp38-macosx_11_0_arm64.whl", hash = "sha256:b19c9ad992c7883ad84c9b22aaa73562a16b819c1d8db7a1a1a49fb7ec13c7d2", size = 1442108, upload-time = "2024-08-02T17:50:40.824Z" },
        { url = "https://files.pythonhosted.org/packages/97/14/95b3f91f857034686cae0e73006b8391d76a8142d339b42970eaaf0416ea/black-24.8.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:1f13f7f386f86f8121d76599114bb8c17b69d962137fc70efe56137727c7047e", size = 1745786, upload-time = "2024-08-02T17:46:02.939Z" },
        { url = "https://files.pythonhosted.org/packages/95/54/68b8883c8aa258a6dde958cd5bdfada8382bec47c5162f4a01e66d839af1/black-24.8.0-cp38-cp38-win_amd64.whl", hash = "sha256:f490dbd59680d809ca31efdae20e634f3fae27fba3ce0ba3208333b713bc3920", size = 1426754, upload-time = "2024-08-02T17:46:38.603Z" },
        { url = "https://files.pythonhosted.org/packages/13/b2/b3f24fdbb46f0e7ef6238e131f13572ee8279b70f237f221dd168a9dba1a/black-24.8.0-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:eab4dd44ce80dea27dc69db40dab62d4ca96112f87996bca68cd75639aeb2e4c", size = 1631706, upload-time = "2024-08-02T17:49:57.606Z" },
        { url = "https://files.pythonhosted.org/packages/d9/35/31010981e4a05202a84a3116423970fd1a59d2eda4ac0b3570fbb7029ddc/black-24.8.0-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:3c4285573d4897a7610054af5a890bde7c65cb466040c5f0c8b732812d7f0e5e", size = 1457429, upload-time = "2024-08-02T17:49:12.764Z" },
        { url = "https://files.pythonhosted.org/packages/27/25/3f706b4f044dd569a20a4835c3b733dedea38d83d2ee0beb8178a6d44945/black-24.8.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:9e84e33b37be070ba135176c123ae52a51f82306def9f7d063ee302ecab2cf47", size = 1756488, upload-time = "2024-08-02T17:46:08.067Z" },
        { url = "https://files.pythonhosted.org/packages/63/72/79375cd8277cbf1c5670914e6bd4c1b15dea2c8f8e906dc21c448d0535f0/black-24.8.0-cp39-cp39-win_amd64.whl", hash = "sha256:73bbf84ed136e45d451a260c6b73ed674652f90a2b3211d6a35e78054563a9bb", size = 1417721, upload-time = "2024-08-02T17:46:42.637Z" },
        { url = "https://files.pythonhosted.org/packages/27/1e/83fa8a787180e1632c3d831f7e58994d7aaf23a0961320d21e84f922f919/black-24.8.0-py3-none-any.whl", hash = "sha256:972085c618ee94f402da1af548a4f218c754ea7e5dc70acb168bfaca4c2542ed", size = 206504, upload-time = "2024-08-02T17:43:15.747Z" },
    ]
    
    [[package]]
    name = "black"
    version = "25.1.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    dependencies = [
        { name = "click", version = "8.1.8", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version == '3.9.*'" },
        { name = "click", version = "8.2.1", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.10'" },
        { name = "mypy-extensions", marker = "python_full_version >= '3.9'" },
        { name = "packaging", marker = "python_full_version >= '3.9'" },
        { name = "pathspec", marker = "python_full_version >= '3.9'" },
        { name = "platformdirs", version = "4.3.8", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "tomli", marker = "python_full_version >= '3.9' and python_full_version < '3.11'" },
        { name = "typing-extensions", marker = "python_full_version >= '3.9' and python_full_version < '3.11'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/94/49/26a7b0f3f35da4b5a65f081943b7bcd22d7002f5f0fb8098ec1ff21cb6ef/black-25.1.0.tar.gz", hash = "sha256:33496d5cd1222ad73391352b4ae8da15253c5de89b93a80b3e2c8d9a19ec2666", size = 649449, upload-time = "2025-01-29T04:15:40.373Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/4d/3b/4ba3f93ac8d90410423fdd31d7541ada9bcee1df32fb90d26de41ed40e1d/black-25.1.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:759e7ec1e050a15f89b770cefbf91ebee8917aac5c20483bc2d80a6c3a04df32", size = 1629419, upload-time = "2025-01-29T05:37:06.642Z" },
        { url = "https://files.pythonhosted.org/packages/b4/02/0bde0485146a8a5e694daed47561785e8b77a0466ccc1f3e485d5ef2925e/black-25.1.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:0e519ecf93120f34243e6b0054db49c00a35f84f195d5bce7e9f5cfc578fc2da", size = 1461080, upload-time = "2025-01-29T05:37:09.321Z" },
        { url = "https://files.pythonhosted.org/packages/52/0e/abdf75183c830eaca7589144ff96d49bce73d7ec6ad12ef62185cc0f79a2/black-25.1.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:055e59b198df7ac0b7efca5ad7ff2516bca343276c466be72eb04a3bcc1f82d7", size = 1766886, upload-time = "2025-01-29T04:18:24.432Z" },
        { url = "https://files.pythonhosted.org/packages/dc/a6/97d8bb65b1d8a41f8a6736222ba0a334db7b7b77b8023ab4568288f23973/black-25.1.0-cp310-cp310-win_amd64.whl", hash = "sha256:db8ea9917d6f8fc62abd90d944920d95e73c83a5ee3383493e35d271aca872e9", size = 1419404, upload-time = "2025-01-29T04:19:04.296Z" },
        { url = "https://files.pythonhosted.org/packages/7e/4f/87f596aca05c3ce5b94b8663dbfe242a12843caaa82dd3f85f1ffdc3f177/black-25.1.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:a39337598244de4bae26475f77dda852ea00a93bd4c728e09eacd827ec929df0", size = 1614372, upload-time = "2025-01-29T05:37:11.71Z" },
        { url = "https://files.pythonhosted.org/packages/e7/d0/2c34c36190b741c59c901e56ab7f6e54dad8df05a6272a9747ecef7c6036/black-25.1.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:96c1c7cd856bba8e20094e36e0f948718dc688dba4a9d78c3adde52b9e6c2299", size = 1442865, upload-time = "2025-01-29T05:37:14.309Z" },
        { url = "https://files.pythonhosted.org/packages/21/d4/7518c72262468430ead45cf22bd86c883a6448b9eb43672765d69a8f1248/black-25.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:bce2e264d59c91e52d8000d507eb20a9aca4a778731a08cfff7e5ac4a4bb7096", size = 1749699, upload-time = "2025-01-29T04:18:17.688Z" },
        { url = "https://files.pythonhosted.org/packages/58/db/4f5beb989b547f79096e035c4981ceb36ac2b552d0ac5f2620e941501c99/black-25.1.0-cp311-cp311-win_amd64.whl", hash = "sha256:172b1dbff09f86ce6f4eb8edf9dede08b1fce58ba194c87d7a4f1a5aa2f5b3c2", size = 1428028, upload-time = "2025-01-29T04:18:51.711Z" },
        { url = "https://files.pythonhosted.org/packages/83/71/3fe4741df7adf015ad8dfa082dd36c94ca86bb21f25608eb247b4afb15b2/black-25.1.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4b60580e829091e6f9238c848ea6750efed72140b91b048770b64e74fe04908b", size = 1650988, upload-time = "2025-01-29T05:37:16.707Z" },
        { url = "https://files.pythonhosted.org/packages/13/f3/89aac8a83d73937ccd39bbe8fc6ac8860c11cfa0af5b1c96d081facac844/black-25.1.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:1e2978f6df243b155ef5fa7e558a43037c3079093ed5d10fd84c43900f2d8ecc", size = 1453985, upload-time = "2025-01-29T05:37:18.273Z" },
        { url = "https://files.pythonhosted.org/packages/6f/22/b99efca33f1f3a1d2552c714b1e1b5ae92efac6c43e790ad539a163d1754/black-25.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:3b48735872ec535027d979e8dcb20bf4f70b5ac75a8ea99f127c106a7d7aba9f", size = 1783816, upload-time = "2025-01-29T04:18:33.823Z" },
        { url = "https://files.pythonhosted.org/packages/18/7e/a27c3ad3822b6f2e0e00d63d58ff6299a99a5b3aee69fa77cd4b0076b261/black-25.1.0-cp312-cp312-win_amd64.whl", hash = "sha256:ea0213189960bda9cf99be5b8c8ce66bb054af5e9e861249cd23471bd7b0b3ba", size = 1440860, upload-time = "2025-01-29T04:19:12.944Z" },
        { url = "https://files.pythonhosted.org/packages/98/87/0edf98916640efa5d0696e1abb0a8357b52e69e82322628f25bf14d263d1/black-25.1.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:8f0b18a02996a836cc9c9c78e5babec10930862827b1b724ddfe98ccf2f2fe4f", size = 1650673, upload-time = "2025-01-29T05:37:20.574Z" },
        { url = "https://files.pythonhosted.org/packages/52/e5/f7bf17207cf87fa6e9b676576749c6b6ed0d70f179a3d812c997870291c3/black-25.1.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:afebb7098bfbc70037a053b91ae8437c3857482d3a690fefc03e9ff7aa9a5fd3", size = 1453190, upload-time = "2025-01-29T05:37:22.106Z" },
        { url = "https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:030b9759066a4ee5e5aca28c3c77f9c64789cdd4de8ac1df642c40b708be6171", size = 1782926, upload-time = "2025-01-29T04:18:58.564Z" },
        { url = "https://files.pythonhosted.org/packages/cc/64/94eb5f45dcb997d2082f097a3944cfc7fe87e071907f677e80788a2d7b7a/black-25.1.0-cp313-cp313-win_amd64.whl", hash = "sha256:a22f402b410566e2d1c950708c77ebf5ebd5d0d88a6a2e87c86d9fb48afa0d18", size = 1442613, upload-time = "2025-01-29T04:19:27.63Z" },
        { url = "https://files.pythonhosted.org/packages/d3/b6/ae7507470a4830dbbfe875c701e84a4a5fb9183d1497834871a715716a92/black-25.1.0-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:a1ee0a0c330f7b5130ce0caed9936a904793576ef4d2b98c40835d6a65afa6a0", size = 1628593, upload-time = "2025-01-29T05:37:23.672Z" },
        { url = "https://files.pythonhosted.org/packages/24/c1/ae36fa59a59f9363017ed397750a0cd79a470490860bc7713967d89cdd31/black-25.1.0-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:f3df5f1bf91d36002b0a75389ca8663510cf0531cca8aa5c1ef695b46d98655f", size = 1460000, upload-time = "2025-01-29T05:37:25.829Z" },
        { url = "https://files.pythonhosted.org/packages/ac/b6/98f832e7a6c49aa3a464760c67c7856363aa644f2f3c74cf7d624168607e/black-25.1.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:d9e6827d563a2c820772b32ce8a42828dc6790f095f441beef18f96aa6f8294e", size = 1765963, upload-time = "2025-01-29T04:18:38.116Z" },
        { url = "https://files.pythonhosted.org/packages/ce/e9/2cb0a017eb7024f70e0d2e9bdb8c5a5b078c5740c7f8816065d06f04c557/black-25.1.0-cp39-cp39-win_amd64.whl", hash = "sha256:bacabb307dca5ebaf9c118d2d2f6903da0d62c9faa82bd21a33eecc319559355", size = 1419419, upload-time = "2025-01-29T04:18:30.191Z" },
        { url = "https://files.pythonhosted.org/packages/09/71/54e999902aed72baf26bca0d50781b01838251a462612966e9fc4891eadd/black-25.1.0-py3-none-any.whl", hash = "sha256:95e8176dae143ba9097f351d174fdaf0ccd29efb414b362ae3fd72bf0f710717", size = 207646, upload-time = "2025-01-29T04:15:38.082Z" },
    ]
    
    [[package]]
    name = "click"
    version = "8.1.8"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version == '3.9.*'",
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
        "python_full_version < '3.8.1'",
    ]
    dependencies = [
        { name = "colorama", marker = "python_full_version < '3.10' and sys_platform == 'win32'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/b9/2e/0090cbf739cee7d23781ad4b89a9894a41538e4fcf4c31dcdd705b78eb8b/click-8.1.8.tar.gz", hash = "sha256:ed53c9d8990d83c2a27deae68e4ee337473f6330c040a31d4225c9574d16096a", size = 226593, upload-time = "2024-12-21T18:38:44.339Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl", hash = "sha256:63c132bbbed01578a06712a2d1f497bb62d9c1c0d329b7903a866228027263b2", size = 98188, upload-time = "2024-12-21T18:38:41.666Z" },
    ]
    
    [[package]]
    name = "click"
    version = "8.2.1"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
    ]
    dependencies = [
        { name = "colorama", marker = "python_full_version >= '3.10' and sys_platform == 'win32'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/60/6c/8ca2efa64cf75a977a0d7fac081354553ebe483345c734fb6b6515d96bbc/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202", size = 286342, upload-time = "2025-05-20T23:19:49.832Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b", size = 102215, upload-time = "2025-05-20T23:19:47.796Z" },
    ]
    
    [[package]]
    name = "colorama"
    version = "0.4.6"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697, upload-time = "2022-10-25T02:36:22.414Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335, upload-time = "2022-10-25T02:36:20.889Z" },
    ]
    
    [[package]]
    name = "exceptiongroup"
    version = "1.3.0"
    source = { registry = "https://pypi.org/simple" }
    dependencies = [
        { name = "typing-extensions", marker = "python_full_version < '3.13'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/0b/9f/a65090624ecf468cdca03533906e7c69ed7588582240cfe7cc9e770b50eb/exceptiongroup-1.3.0.tar.gz", hash = "sha256:b241f5885f560bc56a59ee63ca4c6a8bfa46ae4ad651af316d4e81817bb9fd88", size = 29749, upload-time = "2025-05-10T17:42:51.123Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/36/f4/c6e662dade71f56cd2f3735141b265c3c79293c109549c1e6933b0651ffc/exceptiongroup-1.3.0-py3-none-any.whl", hash = "sha256:4d111e6e0c13d0644cad6ddaa7ed0261a0b36971f6d23e7ec9b4b9097da78a10", size = 16674, upload-time = "2025-05-10T17:42:49.33Z" },
    ]
    
    [[package]]
    name = "flake8"
    version = "5.0.4"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version < '3.8.1'",
    ]
    dependencies = [
        { name = "mccabe", marker = "python_full_version < '3.8.1'" },
        { name = "pycodestyle", version = "2.9.1", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.8.1'" },
        { name = "pyflakes", version = "2.5.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.8.1'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/ad/00/9808c62b2d529cefc69ce4e4a1ea42c0f855effa55817b7327ec5b75e60a/flake8-5.0.4.tar.gz", hash = "sha256:6fbe320aad8d6b95cec8b8e47bc933004678dc63095be98528b7bdd2a9f510db", size = 145862, upload-time = "2022-08-03T23:21:27.108Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/cf/a0/b881b63a17a59d9d07f5c0cc91a29182c8e8a9aa2bde5b3b2b16519c02f4/flake8-5.0.4-py2.py3-none-any.whl", hash = "sha256:7a1cf6b73744f5806ab95e526f6f0d8c01c66d7bbe349562d22dfca20610b248", size = 61897, upload-time = "2022-08-03T23:21:25.027Z" },
    ]
    
    [[package]]
    name = "flake8"
    version = "7.1.2"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
    ]
    dependencies = [
        { name = "mccabe", marker = "python_full_version >= '3.8.1' and python_full_version < '3.9'" },
        { name = "pycodestyle", version = "2.12.1", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.8.1' and python_full_version < '3.9'" },
        { name = "pyflakes", version = "3.2.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.8.1' and python_full_version < '3.9'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/58/16/3f2a0bb700ad65ac9663262905a025917c020a3f92f014d2ba8964b4602c/flake8-7.1.2.tar.gz", hash = "sha256:c586ffd0b41540951ae41af572e6790dbd49fc12b3aa2541685d253d9bd504bd", size = 48119, upload-time = "2025-02-16T18:45:44.296Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/35/f8/08d37b2cd89da306e3520bd27f8a85692122b42b56c0c2c3784ff09c022f/flake8-7.1.2-py2.py3-none-any.whl", hash = "sha256:1cbc62e65536f65e6d754dfe6f1bada7f5cf392d6f5db3c2b85892466c3e7c1a", size = 57745, upload-time = "2025-02-16T18:45:42.351Z" },
    ]
    
    [[package]]
    name = "flake8"
    version = "7.2.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    dependencies = [
        { name = "mccabe", marker = "python_full_version >= '3.9'" },
        { name = "pycodestyle", version = "2.13.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "pyflakes", version = "3.3.2", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/e7/c4/5842fc9fc94584c455543540af62fd9900faade32511fab650e9891ec225/flake8-7.2.0.tar.gz", hash = "sha256:fa558ae3f6f7dbf2b4f22663e5343b6b6023620461f8d4ff2019ef4b5ee70426", size = 48177, upload-time = "2025-03-29T20:08:39.329Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/83/5c/0627be4c9976d56b1217cb5187b7504e7fd7d3503f8bfd312a04077bd4f7/flake8-7.2.0-py2.py3-none-any.whl", hash = "sha256:93b92ba5bdb60754a6da14fa3b93a9361fd00a59632ada61fd7b130436c40343", size = 57786, upload-time = "2025-03-29T20:08:37.902Z" },
    ]
    
    [[package]]
    name = "iniconfig"
    version = "2.1.0"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/f2/97/ebf4da567aa6827c909642694d71c9fcf53e5b504f2d96afea02718862f3/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7", size = 4793, upload-time = "2025-03-19T20:09:59.721Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760", size = 6050, upload-time = "2025-03-19T20:10:01.071Z" },
    ]
    
    [[package]]
    name = "mccabe"
    version = "0.7.0"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/e7/ff/0ffefdcac38932a54d2b5eed4e0ba8a408f215002cd178ad1df0f2806ff8/mccabe-0.7.0.tar.gz", hash = "sha256:348e0240c33b60bbdf4e523192ef919f28cb2c3d7d5c7794f74009290f236325", size = 9658, upload-time = "2022-01-24T01:14:51.113Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl", hash = "sha256:6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e", size = 7350, upload-time = "2022-01-24T01:14:49.62Z" },
    ]
    
    [[package]]
    name = "mypy-extensions"
    version = "1.1.0"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/a2/6e/371856a3fb9d31ca8dac321cda606860fa4548858c0cc45d9d1d4ca2628b/mypy_extensions-1.1.0.tar.gz", hash = "sha256:52e68efc3284861e772bbcd66823fde5ae21fd2fdb51c62a211403730b916558", size = 6343, upload-time = "2025-04-22T14:54:24.164Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl", hash = "sha256:1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505", size = 4963, upload-time = "2025-04-22T14:54:22.983Z" },
    ]
    
    [[package]]
    name = "packaging"
    version = "25.0"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727, upload-time = "2025-04-19T11:48:59.673Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469, upload-time = "2025-04-19T11:48:57.875Z" },
    ]
    
    [[package]]
    name = "pathspec"
    version = "0.12.1"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/ca/bc/f35b8446f4531a7cb215605d100cd88b7ac6f44ab3fc94870c120ab3adbf/pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712", size = 51043, upload-time = "2023-12-10T22:30:45Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08", size = 31191, upload-time = "2023-12-10T22:30:43.14Z" },
    ]
    
    [[package]]
    name = "platformdirs"
    version = "4.3.6"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
        "python_full_version < '3.8.1'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/13/fc/128cc9cb8f03208bdbf93d3aa862e16d376844a14f9a0ce5cf4507372de4/platformdirs-4.3.6.tar.gz", hash = "sha256:357fb2acbc885b0419afd3ce3ed34564c13c9b95c89360cd9563f73aa5e2b907", size = 21302, upload-time = "2024-09-17T19:06:50.688Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/3c/a6/bc1012356d8ece4d66dd75c4b9fc6c1f6650ddd5991e421177d9f8f671be/platformdirs-4.3.6-py3-none-any.whl", hash = "sha256:73e575e1408ab8103900836b97580d5307456908a03e92031bab39e4554cc3fb", size = 18439, upload-time = "2024-09-17T19:06:49.212Z" },
    ]
    
    [[package]]
    name = "platformdirs"
    version = "4.3.8"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/fe/8b/3c73abc9c759ecd3f1f7ceff6685840859e8070c4d947c93fae71f6a0bf2/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc", size = 21362, upload-time = "2025-05-07T22:47:42.121Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4", size = 18567, upload-time = "2025-05-07T22:47:40.376Z" },
    ]
    
    [[package]]
    name = "pluggy"
    version = "1.5.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
        "python_full_version < '3.8.1'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/96/2d/02d4312c973c6050a18b314a5ad0b3210edb65a906f868e31c111dede4a6/pluggy-1.5.0.tar.gz", hash = "sha256:2cffa88e94fdc978c4c574f15f9e59b7f4201d439195c3715ca9e2486f1d0cf1", size = 67955, upload-time = "2024-04-20T21:34:42.531Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/88/5f/e351af9a41f866ac3f1fac4ca0613908d9a41741cfcf2228f4ad853b697d/pluggy-1.5.0-py3-none-any.whl", hash = "sha256:44e1ad92c8ca002de6377e165f3e0f1be63266ab4d554740532335b9d75ea669", size = 20556, upload-time = "2024-04-20T21:34:40.434Z" },
    ]
    
    [[package]]
    name = "pluggy"
    version = "1.6.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/f9/e2/3e91f31a7d2b083fe6ef3fa267035b518369d9511ffab804f839851d2779/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3", size = 69412, upload-time = "2025-05-15T12:30:07.975Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746", size = 20538, upload-time = "2025-05-15T12:30:06.134Z" },
    ]
    
    [[package]]
    name = "pycodestyle"
    version = "2.9.1"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version < '3.8.1'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/b6/83/5bcaedba1f47200f0665ceb07bcb00e2be123192742ee0edfb66b600e5fd/pycodestyle-2.9.1.tar.gz", hash = "sha256:2c9607871d58c76354b697b42f5d57e1ada7d261c261efac224b664affdc5785", size = 102127, upload-time = "2022-08-03T23:13:29.715Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/67/e4/fc77f1039c34b3612c4867b69cbb2b8a4e569720b1f19b0637002ee03aff/pycodestyle-2.9.1-py2.py3-none-any.whl", hash = "sha256:d1735fc58b418fd7c5f658d28d943854f8a849b01a5d0a1e6f3f3fdd0166804b", size = 41493, upload-time = "2022-08-03T23:13:27.416Z" },
    ]
    
    [[package]]
    name = "pycodestyle"
    version = "2.12.1"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/43/aa/210b2c9aedd8c1cbeea31a50e42050ad56187754b34eb214c46709445801/pycodestyle-2.12.1.tar.gz", hash = "sha256:6838eae08bbce4f6accd5d5572075c63626a15ee3e6f842df996bf62f6d73521", size = 39232, upload-time = "2024-08-04T20:26:54.576Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/3a/d8/a211b3f85e99a0daa2ddec96c949cac6824bd305b040571b82a03dd62636/pycodestyle-2.12.1-py2.py3-none-any.whl", hash = "sha256:46f0fb92069a7c28ab7bb558f05bfc0110dac69a0cd23c61ea0040283a9d78b3", size = 31284, upload-time = "2024-08-04T20:26:53.173Z" },
    ]
    
    [[package]]
    name = "pycodestyle"
    version = "2.13.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/04/6e/1f4a62078e4d95d82367f24e685aef3a672abfd27d1a868068fed4ed2254/pycodestyle-2.13.0.tar.gz", hash = "sha256:c8415bf09abe81d9c7f872502a6eee881fbe85d8763dd5b9924bb0a01d67efae", size = 39312, upload-time = "2025-03-29T17:33:30.669Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/07/be/b00116df1bfb3e0bb5b45e29d604799f7b91dd861637e4d448b4e09e6a3e/pycodestyle-2.13.0-py2.py3-none-any.whl", hash = "sha256:35863c5974a271c7a726ed228a14a4f6daf49df369d8c50cd9a6f58a5e143ba9", size = 31424, upload-time = "2025-03-29T17:33:29.405Z" },
    ]
    
    [[package]]
    name = "pyflakes"
    version = "2.5.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version < '3.8.1'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/07/92/f0cb5381f752e89a598dd2850941e7f570ac3cb8ea4a344854de486db152/pyflakes-2.5.0.tar.gz", hash = "sha256:491feb020dca48ccc562a8c0cbe8df07ee13078df59813b83959cbdada312ea3", size = 66388, upload-time = "2022-07-30T17:29:05.816Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/dc/13/63178f59f74e53acc2165aee4b002619a3cfa7eeaeac989a9eb41edf364e/pyflakes-2.5.0-py2.py3-none-any.whl", hash = "sha256:4579f67d887f804e67edb544428f264b7b24f435b263c4614f384135cea553d2", size = 66116, upload-time = "2022-07-30T17:29:04.179Z" },
    ]
    
    [[package]]
    name = "pyflakes"
    version = "3.2.0"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.8.1' and python_full_version < '3.9'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/57/f9/669d8c9c86613c9d568757c7f5824bd3197d7b1c6c27553bc5618a27cce2/pyflakes-3.2.0.tar.gz", hash = "sha256:1c61603ff154621fb2a9172037d84dca3500def8c8b630657d1701f026f8af3f", size = 63788, upload-time = "2024-01-05T00:28:47.703Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/d4/d7/f1b7db88d8e4417c5d47adad627a93547f44bdc9028372dbd2313f34a855/pyflakes-3.2.0-py2.py3-none-any.whl", hash = "sha256:84b5be138a2dfbb40689ca07e2152deb896a65c3a3e24c251c5c62489568074a", size = 62725, upload-time = "2024-01-05T00:28:45.903Z" },
    ]
    
    [[package]]
    name = "pyflakes"
    version = "3.3.2"
    source = { registry = "https://pypi.org/simple" }
    resolution-markers = [
        "python_full_version >= '3.10'",
        "python_full_version == '3.9.*'",
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/af/cc/1df338bd7ed1fa7c317081dcf29bf2f01266603b301e6858856d346a12b3/pyflakes-3.3.2.tar.gz", hash = "sha256:6dfd61d87b97fba5dcfaaf781171ac16be16453be6d816147989e7f6e6a9576b", size = 64175, upload-time = "2025-03-31T13:21:20.34Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/15/40/b293a4fa769f3b02ab9e387c707c4cbdc34f073f945de0386107d4e669e6/pyflakes-3.3.2-py2.py3-none-any.whl", hash = "sha256:5039c8339cbb1944045f4ee5466908906180f13cc99cc9949348d10f82a5c32a", size = 63164, upload-time = "2025-03-31T13:21:18.503Z" },
    ]
    
    [[package]]
    name = "pytest"
    version = "8.3.5"
    source = { registry = "https://pypi.org/simple" }
    dependencies = [
        { name = "colorama", marker = "sys_platform == 'win32'" },
        { name = "exceptiongroup", marker = "python_full_version < '3.11'" },
        { name = "iniconfig" },
        { name = "packaging" },
        { name = "pluggy", version = "1.5.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.9'" },
        { name = "pluggy", version = "1.6.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "tomli", marker = "python_full_version < '3.11'" },
    ]
    sdist = { url = "https://files.pythonhosted.org/packages/ae/3c/c9d525a414d506893f0cd8a8d0de7706446213181570cdbd766691164e40/pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845", size = 1450891, upload-time = "2025-03-02T12:54:54.503Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/30/3d/64ad57c803f1fa1e963a7946b6e0fea4a70df53c1a7fed304586539c2bac/pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820", size = 343634, upload-time = "2025-03-02T12:54:52.069Z" },
    ]
    
    [[package]]
    name = "ssh-key-manager"
    version = "1.0.0"
    source = { editable = "." }
    
    [package.optional-dependencies]
    dev = [
        { name = "black", version = "24.8.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.9'" },
        { name = "black", version = "25.1.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "flake8", version = "5.0.4", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.8.1'" },
        { name = "flake8", version = "7.1.2", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.8.1' and python_full_version < '3.9'" },
        { name = "flake8", version = "7.2.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "pytest" },
    ]
    
    [package.dev-dependencies]
    dev = [
        { name = "black", version = "24.8.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.9'" },
        { name = "black", version = "25.1.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "flake8", version = "5.0.4", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version < '3.8.1'" },
        { name = "flake8", version = "7.1.2", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.8.1' and python_full_version < '3.9'" },
        { name = "flake8", version = "7.2.0", source = { registry = "https://pypi.org/simple" }, marker = "python_full_version >= '3.9'" },
        { name = "pytest" },
    ]
    
    [package.metadata]
    requires-dist = [
        { name = "black", marker = "extra == 'dev'", specifier = ">=22.0.0" },
        { name = "flake8", marker = "extra == 'dev'", specifier = ">=4.0.0" },
        { name = "pytest", marker = "extra == 'dev'", specifier = ">=7.0.0" },
    ]
    provides-extras = ["dev"]
    
    [package.metadata.requires-dev]
    dev = [
        { name = "black", specifier = ">=22.0.0" },
        { name = "flake8", specifier = ">=4.0.0" },
        { name = "pytest", specifier = ">=7.0.0" },
    ]
    
    [[package]]
    name = "tomli"
    version = "2.2.1"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/18/87/302344fed471e44a87289cf4967697d07e532f2421fdaf868a303cbae4ff/tomli-2.2.1.tar.gz", hash = "sha256:cd45e1dc79c835ce60f7404ec8119f2eb06d38b1deba146f07ced3bbc44505ff", size = 17175, upload-time = "2024-11-27T22:38:36.873Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/43/ca/75707e6efa2b37c77dadb324ae7d9571cb424e61ea73fad7c56c2d14527f/tomli-2.2.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:678e4fa69e4575eb77d103de3df8a895e1591b48e740211bd1067378c69e8249", size = 131077, upload-time = "2024-11-27T22:37:54.956Z" },
        { url = "https://files.pythonhosted.org/packages/c7/16/51ae563a8615d472fdbffc43a3f3d46588c264ac4f024f63f01283becfbb/tomli-2.2.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:023aa114dd824ade0100497eb2318602af309e5a55595f76b626d6d9f3b7b0a6", size = 123429, upload-time = "2024-11-27T22:37:56.698Z" },
        { url = "https://files.pythonhosted.org/packages/f1/dd/4f6cd1e7b160041db83c694abc78e100473c15d54620083dbd5aae7b990e/tomli-2.2.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ece47d672db52ac607a3d9599a9d48dcb2f2f735c6c2d1f34130085bb12b112a", size = 226067, upload-time = "2024-11-27T22:37:57.63Z" },
        { url = "https://files.pythonhosted.org/packages/a9/6b/c54ede5dc70d648cc6361eaf429304b02f2871a345bbdd51e993d6cdf550/tomli-2.2.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6972ca9c9cc9f0acaa56a8ca1ff51e7af152a9f87fb64623e31d5c83700080ee", size = 236030, upload-time = "2024-11-27T22:37:59.344Z" },
        { url = "https://files.pythonhosted.org/packages/1f/47/999514fa49cfaf7a92c805a86c3c43f4215621855d151b61c602abb38091/tomli-2.2.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c954d2250168d28797dd4e3ac5cf812a406cd5a92674ee4c8f123c889786aa8e", size = 240898, upload-time = "2024-11-27T22:38:00.429Z" },
        { url = "https://files.pythonhosted.org/packages/73/41/0a01279a7ae09ee1573b423318e7934674ce06eb33f50936655071d81a24/tomli-2.2.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:8dd28b3e155b80f4d54beb40a441d366adcfe740969820caf156c019fb5c7ec4", size = 229894, upload-time = "2024-11-27T22:38:02.094Z" },
        { url = "https://files.pythonhosted.org/packages/55/18/5d8bc5b0a0362311ce4d18830a5d28943667599a60d20118074ea1b01bb7/tomli-2.2.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:e59e304978767a54663af13c07b3d1af22ddee3bb2fb0618ca1593e4f593a106", size = 245319, upload-time = "2024-11-27T22:38:03.206Z" },
        { url = "https://files.pythonhosted.org/packages/92/a3/7ade0576d17f3cdf5ff44d61390d4b3febb8a9fc2b480c75c47ea048c646/tomli-2.2.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:33580bccab0338d00994d7f16f4c4ec25b776af3ffaac1ed74e0b3fc95e885a8", size = 238273, upload-time = "2024-11-27T22:38:04.217Z" },
        { url = "https://files.pythonhosted.org/packages/72/6f/fa64ef058ac1446a1e51110c375339b3ec6be245af9d14c87c4a6412dd32/tomli-2.2.1-cp311-cp311-win32.whl", hash = "sha256:465af0e0875402f1d226519c9904f37254b3045fc5084697cefb9bdde1ff99ff", size = 98310, upload-time = "2024-11-27T22:38:05.908Z" },
        { url = "https://files.pythonhosted.org/packages/6a/1c/4a2dcde4a51b81be3530565e92eda625d94dafb46dbeb15069df4caffc34/tomli-2.2.1-cp311-cp311-win_amd64.whl", hash = "sha256:2d0f2fdd22b02c6d81637a3c95f8cd77f995846af7414c5c4b8d0545afa1bc4b", size = 108309, upload-time = "2024-11-27T22:38:06.812Z" },
        { url = "https://files.pythonhosted.org/packages/52/e1/f8af4c2fcde17500422858155aeb0d7e93477a0d59a98e56cbfe75070fd0/tomli-2.2.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4a8f6e44de52d5e6c657c9fe83b562f5f4256d8ebbfe4ff922c495620a7f6cea", size = 132762, upload-time = "2024-11-27T22:38:07.731Z" },
        { url = "https://files.pythonhosted.org/packages/03/b8/152c68bb84fc00396b83e7bbddd5ec0bd3dd409db4195e2a9b3e398ad2e3/tomli-2.2.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8d57ca8095a641b8237d5b079147646153d22552f1c637fd3ba7f4b0b29167a8", size = 123453, upload-time = "2024-11-27T22:38:09.384Z" },
        { url = "https://files.pythonhosted.org/packages/c8/d6/fc9267af9166f79ac528ff7e8c55c8181ded34eb4b0e93daa767b8841573/tomli-2.2.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4e340144ad7ae1533cb897d406382b4b6fede8890a03738ff1683af800d54192", size = 233486, upload-time = "2024-11-27T22:38:10.329Z" },
        { url = "https://files.pythonhosted.org/packages/5c/51/51c3f2884d7bab89af25f678447ea7d297b53b5a3b5730a7cb2ef6069f07/tomli-2.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:db2b95f9de79181805df90bedc5a5ab4c165e6ec3fe99f970d0e302f384ad222", size = 242349, upload-time = "2024-11-27T22:38:11.443Z" },
        { url = "https://files.pythonhosted.org/packages/ab/df/bfa89627d13a5cc22402e441e8a931ef2108403db390ff3345c05253935e/tomli-2.2.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:40741994320b232529c802f8bc86da4e1aa9f413db394617b9a256ae0f9a7f77", size = 252159, upload-time = "2024-11-27T22:38:13.099Z" },
        { url = "https://files.pythonhosted.org/packages/9e/6e/fa2b916dced65763a5168c6ccb91066f7639bdc88b48adda990db10c8c0b/tomli-2.2.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:400e720fe168c0f8521520190686ef8ef033fb19fc493da09779e592861b78c6", size = 237243, upload-time = "2024-11-27T22:38:14.766Z" },
        { url = "https://files.pythonhosted.org/packages/b4/04/885d3b1f650e1153cbb93a6a9782c58a972b94ea4483ae4ac5cedd5e4a09/tomli-2.2.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:02abe224de6ae62c19f090f68da4e27b10af2b93213d36cf44e6e1c5abd19fdd", size = 259645, upload-time = "2024-11-27T22:38:15.843Z" },
        { url = "https://files.pythonhosted.org/packages/9c/de/6b432d66e986e501586da298e28ebeefd3edc2c780f3ad73d22566034239/tomli-2.2.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:b82ebccc8c8a36f2094e969560a1b836758481f3dc360ce9a3277c65f374285e", size = 244584, upload-time = "2024-11-27T22:38:17.645Z" },
        { url = "https://files.pythonhosted.org/packages/1c/9a/47c0449b98e6e7d1be6cbac02f93dd79003234ddc4aaab6ba07a9a7482e2/tomli-2.2.1-cp312-cp312-win32.whl", hash = "sha256:889f80ef92701b9dbb224e49ec87c645ce5df3fa2cc548664eb8a25e03127a98", size = 98875, upload-time = "2024-11-27T22:38:19.159Z" },
        { url = "https://files.pythonhosted.org/packages/ef/60/9b9638f081c6f1261e2688bd487625cd1e660d0a85bd469e91d8db969734/tomli-2.2.1-cp312-cp312-win_amd64.whl", hash = "sha256:7fc04e92e1d624a4a63c76474610238576942d6b8950a2d7f908a340494e67e4", size = 109418, upload-time = "2024-11-27T22:38:20.064Z" },
        { url = "https://files.pythonhosted.org/packages/04/90/2ee5f2e0362cb8a0b6499dc44f4d7d48f8fff06d28ba46e6f1eaa61a1388/tomli-2.2.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f4039b9cbc3048b2416cc57ab3bda989a6fcf9b36cf8937f01a6e731b64f80d7", size = 132708, upload-time = "2024-11-27T22:38:21.659Z" },
        { url = "https://files.pythonhosted.org/packages/c0/ec/46b4108816de6b385141f082ba99e315501ccd0a2ea23db4a100dd3990ea/tomli-2.2.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:286f0ca2ffeeb5b9bd4fcc8d6c330534323ec51b2f52da063b11c502da16f30c", size = 123582, upload-time = "2024-11-27T22:38:22.693Z" },
        { url = "https://files.pythonhosted.org/packages/a0/bd/b470466d0137b37b68d24556c38a0cc819e8febe392d5b199dcd7f578365/tomli-2.2.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a92ef1a44547e894e2a17d24e7557a5e85a9e1d0048b0b5e7541f76c5032cb13", size = 232543, upload-time = "2024-11-27T22:38:24.367Z" },
        { url = "https://files.pythonhosted.org/packages/d9/e5/82e80ff3b751373f7cead2815bcbe2d51c895b3c990686741a8e56ec42ab/tomli-2.2.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9316dc65bed1684c9a98ee68759ceaed29d229e985297003e494aa825ebb0281", size = 241691, upload-time = "2024-11-27T22:38:26.081Z" },
        { url = "https://files.pythonhosted.org/packages/05/7e/2a110bc2713557d6a1bfb06af23dd01e7dde52b6ee7dadc589868f9abfac/tomli-2.2.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e85e99945e688e32d5a35c1ff38ed0b3f41f43fad8df0bdf79f72b2ba7bc5272", size = 251170, upload-time = "2024-11-27T22:38:27.921Z" },
        { url = "https://files.pythonhosted.org/packages/64/7b/22d713946efe00e0adbcdfd6d1aa119ae03fd0b60ebed51ebb3fa9f5a2e5/tomli-2.2.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:ac065718db92ca818f8d6141b5f66369833d4a80a9d74435a268c52bdfa73140", size = 236530, upload-time = "2024-11-27T22:38:29.591Z" },
        { url = "https://files.pythonhosted.org/packages/38/31/3a76f67da4b0cf37b742ca76beaf819dca0ebef26d78fc794a576e08accf/tomli-2.2.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:d920f33822747519673ee656a4b6ac33e382eca9d331c87770faa3eef562aeb2", size = 258666, upload-time = "2024-11-27T22:38:30.639Z" },
        { url = "https://files.pythonhosted.org/packages/07/10/5af1293da642aded87e8a988753945d0cf7e00a9452d3911dd3bb354c9e2/tomli-2.2.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:a198f10c4d1b1375d7687bc25294306e551bf1abfa4eace6650070a5c1ae2744", size = 243954, upload-time = "2024-11-27T22:38:31.702Z" },
        { url = "https://files.pythonhosted.org/packages/5b/b9/1ed31d167be802da0fc95020d04cd27b7d7065cc6fbefdd2f9186f60d7bd/tomli-2.2.1-cp313-cp313-win32.whl", hash = "sha256:d3f5614314d758649ab2ab3a62d4f2004c825922f9e370b29416484086b264ec", size = 98724, upload-time = "2024-11-27T22:38:32.837Z" },
        { url = "https://files.pythonhosted.org/packages/c7/32/b0963458706accd9afcfeb867c0f9175a741bf7b19cd424230714d722198/tomli-2.2.1-cp313-cp313-win_amd64.whl", hash = "sha256:a38aa0308e754b0e3c67e344754dff64999ff9b513e691d0e786265c93583c69", size = 109383, upload-time = "2024-11-27T22:38:34.455Z" },
        { url = "https://files.pythonhosted.org/packages/6e/c2/61d3e0f47e2b74ef40a68b9e6ad5984f6241a942f7cd3bbfbdbd03861ea9/tomli-2.2.1-py3-none-any.whl", hash = "sha256:cb55c73c5f4408779d0cf3eef9f762b9c9f147a77de7b258bef0a5628adc85cc", size = 14257, upload-time = "2024-11-27T22:38:35.385Z" },
    ]
    
    [[package]]
    name = "typing-extensions"
    version = "4.13.2"
    source = { registry = "https://pypi.org/simple" }
    sdist = { url = "https://files.pythonhosted.org/packages/f6/37/23083fcd6e35492953e8d2aaaa68b860eb422b34627b13f2ce3eb6106061/typing_extensions-4.13.2.tar.gz", hash = "sha256:e6c81219bd689f51865d9e372991c540bda33a0379d5573cddb9a3a23f7caaef", size = 106967, upload-time = "2025-04-10T14:19:05.416Z" }
    wheels = [
        { url = "https://files.pythonhosted.org/packages/8b/54/b1ae86c0973cc6f0210b53d508ca3641fb6d0c56823f288d108bc7ab3cc8/typing_extensions-4.13.2-py3-none-any.whl", hash = "sha256:a439e7c04b49fec3e5d3e2beaa21755cadbbdc391694e28ccdd36ca4a1408f8c", size = 45806, upload-time = "2025-04-10T14:19:03.967Z" },
    ]
```

---

#### `src\.gitignore`

    # --- Python Compiled Artifacts ---
    __pycache__/
    *.py[cod]
    *.pyo
    
    # --- Virtual Environments ---
    venv/
    .venv/
    ENV/
    env/
    
    # --- Logs & Temporary Files ---
    logs/
    *.log
    *.tmp
    
    # --- Build & Packaging ---
    build/
    dist/
    *.egg-info/
    
    # --- IDE ---
    .ipynb_checkpoints/
    .mypy_cache/
    .pytest_cache/
    
    # --- System Files ---
    .DS_Store
    
    # --- Environment / Secret Files ---
    .env
    
    # --- Syncthing ---
    .stfolder

---

#### `src\code_guidelines.md`

```markdown
    # Code Guidelines
    
    ## Core Principles
    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.
    - Maintain inherent simplicity while providing powerful functionality.
    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.
    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.
    
    ## General Principles
    - Aim for simplicity, clarity, and maintainability in all project aspects
    - Favor composition over inheritance when applicable
    - Prioritize readability and understandability for future developers
    - Ensure all components have a single responsibility
    - Coding standards that promote simplicity and maintainability
    - Document only integral decisions in a highly condensed form
    
    ## Code Organization
    - Evaluate the existing codebase structure and identify patterns and anti-patterns
    - Consolidate related functionality into cohesive modules
    - Minimize dependencies between unrelated components
    - Optimize for developer ergonomics and intuitive navigation
    - Balance file granularity with overall system comprehensibility
```

---

#### `src\main.py`

```python
    #!/usr/bin/env python3
    """SSH Key Manager - Minimalist SSH key generation and management utility."""
    
    import platform
    import shutil
    import subprocess
    import sys
    from pathlib import Path
    
    DEFAULT_ACCOUNT_NAME = "jh.paulsen.wrk"
    DEFAULT_EMAIL_DOMAIN = "gmail.com"
    
    
    def get_hostname():
        return platform.node().split(".")[0]
    
    
    def get_ssh_dir():
        return Path.home() / ".ssh"
    
    
    def get_key_path(name=None):
        ssh_dir = get_ssh_dir()
        hostname = get_hostname()
        if not name:
            name = DEFAULT_ACCOUNT_NAME
        key_name = f"{hostname}.{name}.id_rsa"
        return ssh_dir / key_name
    
    
    def run_command(cmd, input_data=None):
        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, input=input_data
            )
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    
    def copy_to_clipboard(text):
        try:
            if platform.system() == 'Windows':
                cmd = ['clip']
            elif platform.system() == 'Darwin':
                cmd = ['pbcopy']
            else:
                cmd = ['xclip', '-selection', 'clipboard']
            run_command(cmd, text)
            return True
        except Exception:
            return False
    
    
    def create_key(name=None, email=None, key_type="rsa", key_bits=4096):
        if not name:
            name = DEFAULT_ACCOUNT_NAME
        if not email:
            email = f"{name}@{DEFAULT_EMAIL_DOMAIN}"
    
        key_path = get_key_path(name)
        ssh_dir = get_ssh_dir()
        ssh_dir.mkdir(exist_ok=True)
    
        cmd = [
            "ssh-keygen", "-o", "-t", key_type, "-b", str(key_bits),
            "-C", email, "-f", str(key_path), "-N", ""
        ]
    
        print(f"Generating {key_bits}-bit {key_type} key for {email}...")
        print(f"Key will be saved to: {key_path}")
    
        result = run_command(cmd)
    
        if result["success"]:
            return {
                "success": True,
                "key_path": key_path,
                "public_key_path": str(key_path) + ".pub",
                "message": f"Successfully generated key: {key_path}"
            }
        else:
            return {
                "success": False,
                "error": result.get("stderr", "Unknown error during key generation")
            }
    
    
    def check_windows_openssh():
        if platform.system() != "Windows":
            return {"success": True, "message": "Not Windows - no check needed"}
    
        if not shutil.which("ssh-agent"):
            return {
                "success": False,
                "error": "OpenSSH Client not found",
                "fix_command": ('Add-WindowsCapability -Online '
                                '-Name OpenSSH.Client~~~~0.0.1.0')
            }
    
        check_service_cmd = [
            "powershell", "-Command",
            ("Get-Service ssh-agent -ErrorAction SilentlyContinue | "
             "Select-Object Status, StartType")
        ]
        result = run_command(check_service_cmd)
    
        if not result["success"] or "ssh-agent" not in result["stdout"]:
            return {
                "success": False,
                "error": "SSH Agent service not found",
                "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
            }
    
        if "Disabled" in result["stdout"]:
            return {
                "success": False,
                "error": "SSH Agent service is disabled",
                "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
            }
    
        return {"success": True, "message": "OpenSSH properly configured"}
    
    
    def fix_windows_openssh():
        print("Attempting to fix OpenSSH configuration...")
    
        install_cmd = [
            "powershell", "-Command",
            "Add-WindowsCapability -Online -Name OpenSSH.Client~~~~0.0.1.0"
        ]
        print("Installing OpenSSH Client...")
        result = run_command(install_cmd)
    
        if not result["success"]:
            return {
                "success": False,
                "error": f"Failed to install OpenSSH Client: {result.get('stderr', 'Unknown error')}"
            }
    
        enable_cmd = [
            "powershell", "-Command",
            "Set-Service -Name ssh-agent -StartupType Manual"
        ]
        print("Enabling SSH Agent service...")
        result = run_command(enable_cmd)
    
        if not result["success"]:
            return {
                "success": False,
                "error": f"Failed to enable SSH Agent service: {result.get('stderr', 'Unknown error')}"
            }
    
        return {
            "success": True,
            "message": "OpenSSH Client installed and SSH Agent service enabled"
        }
    
    
    def agent_operation(operation, key_path=None):
        if operation == "start":
            if platform.system() == "Windows":
                check_result = check_windows_openssh()
                if not check_result["success"]:
                    print(f"⚠️  OpenSSH issue detected: {check_result['error']}")
                    print("Attempting to fix automatically...")
    
                    fix_result = fix_windows_openssh()
                    if not fix_result["success"]:
                        return {
                            "success": False,
                            "error": f"OpenSSH configuration failed: {fix_result['error']}",
                            "manual_fix": f"Run as Administrator: {check_result.get('fix_command', 'Install OpenSSH')}"
                        }
                    print(f"✓ {fix_result['message']}")
    
                print("Restarting SSH agent on Windows...")
                stop_cmd = ["powershell", "-Command", "Stop-Service ssh-agent -ErrorAction SilentlyContinue"]
                run_command(stop_cmd)
    
                start_cmd = ["powershell", "-Command", "Start-Service ssh-agent"]
                result = run_command(start_cmd)
    
                if not result["success"]:
                    print("Using ssh-agent directly...")
                    result = run_command(["ssh-agent", "-s"])
            else:
                print("Restarting SSH agent...")
                run_command(["ssh-agent", "-k"])
                result = run_command(["ssh-agent", "-s"])
    
            if result["success"]:
                return {"success": True, "message": "SSH agent started successfully"}
            else:
                return {"success": False, "error": result.get("stderr", "Failed to start SSH agent")}
    
        elif operation == "list":
            print("Listing keys in SSH agent...")
            result = run_command(["ssh-add", "-l"])
    
            if result["returncode"] == 0:
                if not result["stdout"].strip():
                    return {"success": True, "message": "No keys loaded in agent"}
                return {"success": True, "keys": result["stdout"]}
            else:
                if "The agent has no identities" in result["stderr"]:
                    return {"success": True, "message": "No keys loaded in agent"}
                return {"success": False, "error": result["stderr"]}
    
        elif operation == "add":
            if not key_path:
                key_path = get_key_path()
    
            print(f"Adding key to SSH agent: {key_path}")
            result = run_command(["ssh-add", str(key_path)])
    
            if result["success"]:
                return {"success": True, "message": f"Added key: {key_path}"}
            else:
                return {"success": False, "error": result.get("stderr", f"Failed to add key: {key_path}")}
    
        elif operation == "clear":
            print("Clearing all keys from SSH agent...")
            result = run_command(["ssh-add", "-D"])
    
            if result["success"]:
                return {"success": True, "message": "Cleared all keys from agent"}
            else:
                return {"success": False, "error": result.get("stderr", "Failed to clear keys")}
    
        return {"success": False, "error": f"Unknown operation: {operation}"}
    
    
    def git_operation(operation, param=None, target_dir=None):
        if operation == "test":
            service = param or "gitlab.com"
            print(f"Testing connection to {service}...")
            cmd = ["ssh", "-T", f"git@{service}"]
            result = run_command(cmd)
    
            success_messages = [
                "successfully authenticated",
                "You've successfully authenticated",
                "Hi ",
                "Welcome to GitLab"
            ]
    
            if any(msg in result["stderr"] for msg in success_messages):
                return {
                    "success": True,
                    "message": f"Successfully connected to {service}",
                    "details": result["stderr"]
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to connect to {service}",
                    "details": result.get("stderr", result.get("stdout", "No output"))
                }
    
        elif operation == "clone":
            if not param:
                return {"success": False, "error": "No repository URL provided"}
    
            print(f"Cloning repository: {param}")
            cmd = ["git", "clone", param]
            if target_dir:
                cmd.append(target_dir)
    
            result = run_command(cmd)
    
            if result["success"]:
                return {"success": True, "message": "Successfully cloned repository"}
            else:
                return {"success": False, "error": result.get("stderr", "Failed to clone repository")}
    
        elif operation == "remote":
            if not param:
                return {"success": False, "error": "No remote URL provided"}
    
            print(f"Adding remote origin: {param}")
            cmd = ["git", "remote", "add", "origin", param]
            result = run_command(cmd)
    
            if result["success"]:
                verify = run_command(["git", "remote", "-v"])
                return {
                    "success": True,
                    "message": "Remote added successfully",
                    "remotes": verify.get("stdout", "")
                }
            else:
                return {"success": False, "error": result.get("stderr", "Failed to add remote")}
    
        return {"success": False, "error": f"Unknown operation: {operation}"}
    
    
    def show_key(name=None, copy_to_clipboard_flag=False):
        key_path = get_key_path(name).with_suffix(".pub")
    
        if not key_path.exists():
            return {
                "success": False,
                "error": f"Public key not found: {key_path}"
            }
    
        try:
            with open(key_path, 'r') as f:
                key_content = f.read().strip()
    
            result = {
                "success": True,
                "key": key_content,
                "key_path": key_path
            }
    
            if copy_to_clipboard_flag:
                if copy_to_clipboard(key_content):
                    result["clipboard_msg"] = "Key copied to clipboard"
                else:
                    result["clipboard_msg"] = "Failed to copy to clipboard"
    
            return result
    
        except Exception as e:
            return {"success": False, "error": f"Failed to read key: {e}"}
    
    
    def interactive_session():
        while True:
            print("\n===== SSH Key Manager =====")
            print("What would you like to do?")
            print("1. Create new SSH key")
            print("2. Manage SSH agent")
            print("3. Work with Git repository")
            print("4. View/copy existing key")
            print("5. Exit")
    
            choice = input("\n> ")
    
            if choice == "1":
                print("\n--- Create SSH Key ---")
                name = input(f"Key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
                default_email = f"{name or DEFAULT_ACCOUNT_NAME}@{DEFAULT_EMAIL_DOMAIN}"
                email = input(f"Email [{default_email}]: ").strip() or None
    
                result = create_key(name=name, email=email)
    
                if result["success"]:
                    print(f"\n✓ {result['message']}")
    
                    add_result = agent_operation("add", result["key_path"])
                    if add_result["success"]:
                        print("✓ Key automatically added to SSH agent")
    
                    key_result = show_key(name)
                    if key_result["success"]:
                        print("\nYour public key:")
                        print(key_result["key"])
                        print("\nAdd this key to your Git service (GitLab/GitHub)")
                        print("GitLab: https://gitlab.com/-/profile/keys")
                        print("GitHub: https://github.com/settings/keys")
                else:
                    print(f"\n✗ Error: {result['error']}")
    
            elif choice == "2":
                print("\n--- SSH Agent Management ---")
                print("1. Start/restart agent")
                print("2. List loaded keys")
                print("3. Add key to agent")
                print("4. Clear all keys")
                print("5. Back")
    
                agent_choice = input("\n> ")
    
                if agent_choice == "1":
                    result = agent_operation("start")
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
    
                elif agent_choice == "2":
                    result = agent_operation("list")
                    print("\nLoaded keys:")
                    if "keys" in result:
                        print(result["keys"])
                    else:
                        print(result.get("message", "No keys loaded or error checking"))
    
                elif agent_choice == "3":
                    name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
                    key_path = get_key_path(name)
                    result = agent_operation("add", key_path)
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
    
                elif agent_choice == "4":
                    result = agent_operation("clear")
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
    
            elif choice == "3":
                print("\n--- Git Operations ---")
                print("1. Test SSH connection")
                print("2. Clone repository")
                print("3. Add remote origin")
                print("4. Back")
    
                git_choice = input("\n> ")
    
                if git_choice == "1":
                    service = input("Enter Git service [gitlab.com]: ").strip() or "gitlab.com"
                    result = git_operation("test", service)
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
                    if "details" in result:
                        print(f"Details: {result['details']}")
    
                elif git_choice == "2":
                    repo_url = input("Enter repository URL: ").strip()
                    target_dir = input("Enter target directory [default]: ").strip() or None
                    if repo_url:
                        result = git_operation("clone", repo_url, target_dir)
                        status = "✓" if result["success"] else "✗"
                        msg = result.get("message", result.get("error", ""))
                        print(f"\n{status} {msg}")
                    else:
                        print("\n✗ No repository URL provided")
    
                elif git_choice == "3":
                    remote_url = input("Enter remote URL: ").strip()
                    if remote_url:
                        result = git_operation("remote", remote_url)
                        status = "✓" if result["success"] else "✗"
                        msg = result.get("message", result.get("error", ""))
                        print(f"\n{status} {msg}")
                        if result["success"] and "remotes" in result:
                            print("\nConfigured remotes:")
                            print(result["remotes"])
                    else:
                        print("\n✗ No remote URL provided")
    
            elif choice == "4":
                print("\n--- View/Copy SSH Key ---")
                name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
                copy = input("Copy to clipboard? (y/n) [n]: ").lower().startswith("y")
    
                result = show_key(name, copy)
    
                if result["success"]:
                    print(f"\nPublic key ({result['key_path']}):")
                    print(result["key"])
    
                    if result.get("clipboard_msg"):
                        print(f"\n✓ {result['clipboard_msg']}")
    
                    print("\nAdd this key to your Git service:")
                    print("GitLab: https://gitlab.com/-/profile/keys")
                    print("GitHub: https://github.com/settings/keys")
                else:
                    print(f"\n✗ Error: {result['error']}")
    
            elif choice == "5":
                print("\nGoodbye!")
                break
    
            else:
                print("\n✗ Invalid choice. Please try again.")
    
    
    if __name__ == "__main__":
        if not shutil.which("ssh-keygen"):
            print("ERROR: ssh-keygen not found. Please install SSH.")
            sys.exit(1)
    
        if len(sys.argv) == 1:
            interactive_session()
            sys.exit(0)
    
        # Command line mode would go here
        print("Command line mode not implemented in this simplified version")
```

